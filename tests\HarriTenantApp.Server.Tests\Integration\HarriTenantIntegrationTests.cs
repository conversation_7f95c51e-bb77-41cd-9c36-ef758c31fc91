using FluentAssertions;
using HarriTenantApp.Server.Data;
using HarriTenantApp.Server.Models;
using HarriTenantApp.Server.Repositories;
using HarriTenantApp.Server.Services;
using Microsoft.EntityFrameworkCore;

namespace HarriTenantApp.Server.Tests.Integration
{
    public class HarriTenantIntegrationTests : TestBase
    {
        [Theory]
        [AutoMoqData]
        public async Task ShouldCompleteFullCrudWorkflow_WhenAllSystemsWorking(HarriTenant tenant)
        {
            // Arrange
            using var context = CreateInMemoryDbContext();
            var repository = new HarriTenantRepository(context, CreateMockLogger<HarriTenantRepository>());
            var service = new HarriTenantService(repository, CreateMockLogger<HarriTenantService>());

            // Act & Assert - Create
            var createResult = await service.CreateTenantAsync(tenant);
            createResult.IsSuccess.Should().BeTrue();
            createResult.Data.Should().NotBeNull();
            createResult.Data!.TenantId.Should().Be(tenant.TenantId);

            // Act & Assert - Read (Get by ID)
            var getByIdResult = await service.GetTenantByIdAsync(tenant.TenantId);
            getByIdResult.IsSuccess.Should().BeTrue();
            getByIdResult.Data.Should().NotBeNull();
            getByIdResult.Data!.Client.Should().Be(tenant.Client);

            // Act & Assert - Read (Get All)
            var getAllResult = await service.GetAllTenantsAsync();
            getAllResult.IsSuccess.Should().BeTrue();
            getAllResult.Data.Should().NotBeNull();
            getAllResult.Data!.Should().HaveCount(1);
            getAllResult.Data!.First().TenantId.Should().Be(tenant.TenantId);

            // Act & Assert - Update
            var updatedClient = "Updated Client Name";
            getByIdResult.Data!.Client = updatedClient;
            var updateResult = await service.UpdateTenantAsync(getByIdResult.Data);
            updateResult.IsSuccess.Should().BeTrue();
            updateResult.Data!.Client.Should().Be(updatedClient);

            // Verify update persisted
            var verifyUpdateResult = await service.GetTenantByIdAsync(tenant.TenantId);
            verifyUpdateResult.Data!.Client.Should().Be(updatedClient);

            // Act & Assert - Delete
            var deleteResult = await service.DeleteTenantAsync(tenant.TenantId);
            deleteResult.IsSuccess.Should().BeTrue();

            // Verify deletion
            var verifyDeleteResult = await service.GetTenantByIdAsync(tenant.TenantId);
            verifyDeleteResult.IsSuccess.Should().BeFalse();
            verifyDeleteResult.ErrorMessage.Should().Contain("not found");
        }

        [Theory]
        [AutoMoqData]
        public async Task ShouldHandleMultipleTenants_WhenWorkingWithCollection(List<HarriTenant> tenants)
        {
            // Arrange
            using var context = CreateInMemoryDbContext();
            var repository = new HarriTenantRepository(context, CreateMockLogger<HarriTenantRepository>());
            var service = new HarriTenantService(repository, CreateMockLogger<HarriTenantService>());

            // Act - Create multiple tenants
            foreach (var tenant in tenants)
            {
                var result = await service.CreateTenantAsync(tenant);
                result.IsSuccess.Should().BeTrue();
            }

            // Assert - Verify all tenants exist
            var getAllResult = await service.GetAllTenantsAsync();
            getAllResult.IsSuccess.Should().BeTrue();
            getAllResult.Data.Should().HaveCount(tenants.Count);

            // Act & Assert - Verify each tenant individually
            foreach (var tenant in tenants)
            {
                var getResult = await service.GetTenantByIdAsync(tenant.TenantId);
                getResult.IsSuccess.Should().BeTrue();
                getResult.Data!.Client.Should().Be(tenant.Client);
            }

            // Act - Delete half the tenants
            var tenantsToDelete = tenants.Take(tenants.Count / 2).ToList();
            foreach (var tenant in tenantsToDelete)
            {
                var deleteResult = await service.DeleteTenantAsync(tenant.TenantId);
                deleteResult.IsSuccess.Should().BeTrue();
            }

            // Assert - Verify correct number remain
            var finalGetAllResult = await service.GetAllTenantsAsync();
            finalGetAllResult.Data.Should().HaveCount(tenants.Count - tenantsToDelete.Count);
        }

        [Theory]
        [AutoMoqData]
        public async Task ShouldMaintainDataIntegrity_WhenConcurrentOperationsOccur(HarriTenant tenant)
        {
            // Arrange
            using var context = CreateInMemoryDbContext();
            var repository1 = new HarriTenantRepository(context, CreateMockLogger<HarriTenantRepository>());
            var repository2 = new HarriTenantRepository(context, CreateMockLogger<HarriTenantRepository>());
            var service1 = new HarriTenantService(repository1, CreateMockLogger<HarriTenantService>());
            var service2 = new HarriTenantService(repository2, CreateMockLogger<HarriTenantService>());

            // Act - Create tenant with service1
            var createResult = await service1.CreateTenantAsync(tenant);
            createResult.IsSuccess.Should().BeTrue();

            // Act - Read with both services
            var read1Task = service1.GetTenantByIdAsync(tenant.TenantId);
            var read2Task = service2.GetTenantByIdAsync(tenant.TenantId);

            var results = await Task.WhenAll(read1Task, read2Task);

            // Assert - Both reads should succeed
            results[0].IsSuccess.Should().BeTrue();
            results[1].IsSuccess.Should().BeTrue();
            results[0].Data!.Client.Should().Be(tenant.Client);
            results[1].Data!.Client.Should().Be(tenant.Client);
        }

        [Theory]
        [AutoMoqData]
        public async Task ShouldHandleValidationErrors_InFullWorkflow()
        {
            // Arrange
            using var context = CreateInMemoryDbContext();
            var repository = new HarriTenantRepository(context, CreateMockLogger<HarriTenantRepository>());
            var service = new HarriTenantService(repository, CreateMockLogger<HarriTenantService>());

            var invalidTenant = new HarriTenant
            {
                TenantId = Guid.NewGuid(),
                Client = "", // Invalid
                Secret = "123", // Invalid - too short
                TokenUrl = "invalid-url", // Invalid
                BaseUrl = "invalid-url", // Invalid
                IsCorporateTenant = false,
                IsActive = true
            };

            // Act
            var createResult = await service.CreateTenantAsync(invalidTenant);

            // Assert
            createResult.IsSuccess.Should().BeFalse();
            createResult.ValidationErrors.Should().NotBeEmpty();
            createResult.ValidationErrors.Should().HaveCountGreaterThan(1);

            // Verify no data was persisted
            var getAllResult = await service.GetAllTenantsAsync();
            getAllResult.Data.Should().BeEmpty();
        }

        [Theory]
        [AutoMoqData]
        public async Task ShouldHandleRepositoryFailures_Gracefully(HarriTenant tenant)
        {
            // Arrange - Create context that will be disposed to simulate connection failure
            var context = CreateInMemoryDbContext();
            var repository = new HarriTenantRepository(context, CreateMockLogger<HarriTenantRepository>());
            var service = new HarriTenantService(repository, CreateMockLogger<HarriTenantService>());

            // Create tenant first
            await service.CreateTenantAsync(tenant);

            // Dispose context to simulate database failure
            await context.DisposeAsync();

            // Act & Assert - Operations should handle the failure gracefully
            var getResult = await service.GetTenantByIdAsync(tenant.TenantId);
            getResult.IsSuccess.Should().BeFalse();
            getResult.ErrorMessage.Should().NotBeNullOrEmpty();
        }

        [Theory]
        [AutoMoqData]
        public async Task ShouldMaintainConsistency_WhenUpdatingMultipleFields(HarriTenant tenant)
        {
            // Arrange
            using var context = CreateInMemoryDbContext();
            var repository = new HarriTenantRepository(context, CreateMockLogger<HarriTenantRepository>());
            var service = new HarriTenantService(repository, CreateMockLogger<HarriTenantService>());

            // Create initial tenant
            await service.CreateTenantAsync(tenant);

            // Act - Update multiple fields
            var updatedTenant = await service.GetTenantByIdAsync(tenant.TenantId);
            updatedTenant.Data!.Client = "Updated Client";
            updatedTenant.Data.Name = "Updated Name";
            updatedTenant.Data.BaseUrl = "https://updated.com";
            updatedTenant.Data.TokenUrl = "https://updated.com/token";
            updatedTenant.Data.Secret = "UpdatedSecret123";
            updatedTenant.Data.IsCorporateTenant = !tenant.IsCorporateTenant;
            updatedTenant.Data.IsActive = !tenant.IsActive;

            var updateResult = await service.UpdateTenantAsync(updatedTenant.Data);

            // Assert
            updateResult.IsSuccess.Should().BeTrue();

            // Verify all fields were updated
            var verifyResult = await service.GetTenantByIdAsync(tenant.TenantId);
            verifyResult.Data!.Client.Should().Be("Updated Client");
            verifyResult.Data.Name.Should().Be("Updated Name");
            verifyResult.Data.BaseUrl.Should().Be("https://updated.com");
            verifyResult.Data.TokenUrl.Should().Be("https://updated.com/token");
            verifyResult.Data.Secret.Should().Be("UpdatedSecret123");
            verifyResult.Data.IsCorporateTenant.Should().Be(!tenant.IsCorporateTenant);
            verifyResult.Data.IsActive.Should().Be(!tenant.IsActive);
        }

        [Fact]
        public async Task ShouldHandleEmptyDatabase_Gracefully()
        {
            // Arrange
            using var context = CreateInMemoryDbContext();
            var repository = new HarriTenantRepository(context, CreateMockLogger<HarriTenantRepository>());
            var service = new HarriTenantService(repository, CreateMockLogger<HarriTenantService>());

            // Act
            var getAllResult = await service.GetAllTenantsAsync();

            // Assert
            getAllResult.IsSuccess.Should().BeTrue();
            getAllResult.Data.Should().NotBeNull();
            getAllResult.Data.Should().BeEmpty();
        }
    }
}
