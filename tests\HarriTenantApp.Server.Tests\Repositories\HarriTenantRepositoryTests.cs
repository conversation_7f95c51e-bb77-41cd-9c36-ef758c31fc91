using FluentAssertions;
using HarriTenantApp.Server.Models;
using HarriTenantApp.Server.Repositories;
using Microsoft.EntityFrameworkCore;

namespace HarriTenantApp.Server.Tests.Repositories
{
    public class HarriTenantRepositoryTests : TestBase
    {
        [Theory]
        [AutoMoqData]
        public async Task ShouldCreateHarriTenant_WhenValidDataProvided(HarriTenant tenant)
        {
            // Arrange
            using var context = CreateInMemoryDbContext();
            var repository = new HarriTenantRepository(context, CreateMockLogger<HarriTenantRepository>());

            // Act
            var result = await repository.CreateAsync(tenant);

            // Assert
            result.Should().NotBeNull();
            result.TenantId.Should().Be(tenant.TenantId);
            result.Client.Should().Be(tenant.Client);
            
            var savedTenant = await context.HarriTenants.FindAsync(tenant.TenantId);
            savedTenant.Should().NotBeNull();
            savedTenant!.Client.Should().Be(tenant.Client);
        }

        [Theory]
        [AutoMoqData]
        public async Task ShouldRetrieveAllHarriTenants_WhenTenantsExist(List<HarriTenant> tenants)
        {
            // Arrange
            using var context = await CreateDbContextWithDataAsync(tenants.ToArray());
            var repository = new HarriTenantRepository(context, CreateMockLogger<HarriTenantRepository>());

            // Act
            var result = await repository.GetAllAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(tenants.Count);
            result.Select(t => t.TenantId).Should().BeEquivalentTo(tenants.Select(t => t.TenantId));
        }

        [Theory]
        [AutoMoqData]
        public async Task ShouldRetrieveHarriTenantById_WhenRecordExists(HarriTenant tenant)
        {
            // Arrange
            using var context = await CreateDbContextWithDataAsync(tenant);
            var repository = new HarriTenantRepository(context, CreateMockLogger<HarriTenantRepository>());

            // Act
            var result = await repository.GetByIdAsync(tenant.TenantId);

            // Assert
            result.Should().NotBeNull();
            result!.TenantId.Should().Be(tenant.TenantId);
            result.Client.Should().Be(tenant.Client);
        }

        [Theory]
        [AutoMoqData]
        public async Task ShouldReturnNull_WhenHarriTenantNotFound(Guid nonExistentId)
        {
            // Arrange
            using var context = CreateInMemoryDbContext();
            var repository = new HarriTenantRepository(context, CreateMockLogger<HarriTenantRepository>());

            // Act
            var result = await repository.GetByIdAsync(nonExistentId);

            // Assert
            result.Should().BeNull();
        }

        [Theory]
        [AutoMoqData]
        public async Task ShouldUpdateHarriTenant_WhenValidChangesProvided(HarriTenant originalTenant, string newClient)
        {
            // Arrange
            using var context = await CreateDbContextWithDataAsync(originalTenant);
            var repository = new HarriTenantRepository(context, CreateMockLogger<HarriTenantRepository>());
            
            originalTenant.Client = newClient;

            // Act
            var result = await repository.UpdateAsync(originalTenant);

            // Assert
            result.Should().NotBeNull();
            result.Client.Should().Be(newClient);
            
            var updatedTenant = await context.HarriTenants.FindAsync(originalTenant.TenantId);
            updatedTenant!.Client.Should().Be(newClient);
        }

        [Theory]
        [AutoMoqData]
        public async Task ShouldDeleteHarriTenant_WhenRecordExists(HarriTenant tenant)
        {
            // Arrange
            using var context = await CreateDbContextWithDataAsync(tenant);
            var repository = new HarriTenantRepository(context, CreateMockLogger<HarriTenantRepository>());

            // Act
            var result = await repository.DeleteAsync(tenant.TenantId);

            // Assert
            result.Should().BeTrue();

            var deletedTenant = await context.HarriTenants.FindAsync(tenant.TenantId);
            deletedTenant.Should().BeNull();
        }

        [Theory]
        [AutoMoqData]
        public async Task ShouldReturnFalse_WhenDeletingNonExistentHarriTenant(Guid nonExistentId)
        {
            // Arrange
            using var context = CreateInMemoryDbContext();
            var repository = new HarriTenantRepository(context, CreateMockLogger<HarriTenantRepository>());

            // Act
            var result = await repository.DeleteAsync(nonExistentId);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task ShouldReturnEmptyList_WhenNoTenantsExist()
        {
            // Arrange
            using var context = CreateInMemoryDbContext();
            var repository = new HarriTenantRepository(context, CreateMockLogger<HarriTenantRepository>());

            // Act
            var result = await repository.GetAllAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Theory]
        [AutoMoqData]
        public async Task ShouldHandleConcurrentUpdates_WhenMultipleUpdatesOccur(HarriTenant tenant)
        {
            // Arrange
            using var context = await CreateDbContextWithDataAsync(tenant);
            var repository1 = new HarriTenantRepository(context, CreateMockLogger<HarriTenantRepository>());
            var repository2 = new HarriTenantRepository(context, CreateMockLogger<HarriTenantRepository>());

            // Act & Assert
            var tenant1 = await repository1.GetByIdAsync(tenant.TenantId);
            var tenant2 = await repository2.GetByIdAsync(tenant.TenantId);

            tenant1!.Client = "Updated by Repository 1";
            tenant2!.Client = "Updated by Repository 2";

            await repository1.UpdateAsync(tenant1);
            
            // Second update should still work (last write wins in this implementation)
            var result = await repository2.UpdateAsync(tenant2);
            result.Should().NotBeNull();
            result.Client.Should().Be("Updated by Repository 2");
        }
    }
}
