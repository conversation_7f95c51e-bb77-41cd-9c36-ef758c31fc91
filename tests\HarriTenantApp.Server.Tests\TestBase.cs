using AutoFixture;
using AutoFixture.AutoMoq;
using AutoFixture.Xunit2;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using HarriTenantApp.Server.Data;
using HarriTenantApp.Server.Models;

namespace HarriTenantApp.Server.Tests
{
    /// <summary>
    /// Base class for test infrastructure and common test utilities
    /// </summary>
    public abstract class TestBase
    {
        public static IFixture CreateFixture()
        {
            var fixture = new Fixture();
            fixture.Customize(new AutoMoqCustomization());
            
            // Customize HarriTenant creation to ensure valid data
            fixture.Customize<HarriTenant>(composer => composer
                .With(x => x.TenantId, () => Guid.NewGuid())
                .With(x => x.Client, () => fixture.Create<string>()[..Math.Min(50, fixture.Create<string>().Length)])
                .With(x => x.Secret, () => fixture.Create<string>()[..Math.Min(50, fixture.Create<string>().Length)])
                .With(x => x.TokenUrl, () => "https://example.com/token")
                .With(x => x.BaseUrl, () => "https://example.com")
                .With(x => x.Name, () => fixture.Create<string>()[..Math.Min(25, fixture.Create<string>().Length)])
                .With(x => x.IsCorporateTenant, () => fixture.Create<bool>())
                .With(x => x.IsActive, () => fixture.Create<bool>()));

            return fixture;
        }

        protected static ApplicationDbContext CreateInMemoryDbContext(string? databaseName = null)
        {
            databaseName ??= Guid.NewGuid().ToString();
            
            var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase(databaseName)
                .Options;

            return new ApplicationDbContext(options);
        }

        protected static async Task<ApplicationDbContext> CreateDbContextWithDataAsync(params HarriTenant[] tenants)
        {
            var context = CreateInMemoryDbContext();
            
            if (tenants?.Any() == true)
            {
                context.HarriTenants.AddRange(tenants);
                await context.SaveChangesAsync();
            }

            return context;
        }

        protected static ILogger<T> CreateMockLogger<T>()
        {
            return new Mock<ILogger<T>>().Object;
        }
    }

    /// <summary>
    /// AutoMoqData attribute for parameterized tests with AutoFixture and Moq
    /// </summary>
    public class AutoMoqDataAttribute : AutoDataAttribute
    {
        public AutoMoqDataAttribute() : base(() => TestBase.CreateFixture())
        {
        }
    }

    /// <summary>
    /// InlineAutoMoqData attribute for parameterized tests with inline data and AutoFixture
    /// </summary>
    public class InlineAutoMoqDataAttribute : InlineAutoDataAttribute
    {
        public InlineAutoMoqDataAttribute(params object[] values) : base(new AutoMoqDataAttribute(), values)
        {
        }
    }
}
