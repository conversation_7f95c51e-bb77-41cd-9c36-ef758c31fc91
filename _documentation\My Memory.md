---
purpose: "AI Knowledge Base for Project Context and Patterns - Prevents Multiple Codebase Analysis"
audience: "AI Assistants (Primary), Human Developers (Secondary)"
instructions: |
  This file serves as persistent memory for AI assistants working on this project.
  Document architectural decisions, coding patterns, dependencies, known issues,
  and lessons learned to prevent redundant codebase analysis and multiple explorations
  of the same code sections. Always consult this file FIRST before examining code.
---

# My Memory

This file is the AI knowledge base for the project. Add architectural notes, patterns, dependencies, and lessons learned here.

## Project Structure (Last Updated: June 5, 2025)

### Root Directory Structure
```
c:\dev\master3\HarriTenantApp\HarriTenantAugment\
├── HarriTenantApp.sln              # Visual Studio Solution File
├── .gitignore                      # Git ignore patterns for .NET projects
├── .git/                           # Git repository
├── .Github/                        # GitHub-specific files
│   └── Instructions/               # Custom coding standards
├── _Documentation/                 # Project documentation (solution folder)
│   ├── HarrTenantPRD.md           # Product Requirements Document
│   ├── How To Guide.md            # Setup and usage guide
│   ├── My Memory.md               # AI knowledge base (this file)
│   ├── PromptTranscript.md        # Verbatim conversation log
│   ├── Support Documentation.md   # Technical support info
│   ├── Tasks.md                   # Project task breakdown
│   └── Unit Tests.md              # Unit test specifications
├── src/                           # Source code projects
│   ├── HarriTenantApp.Server/     # Blazor Server Application
│   └── HarriTenantApp.Shared/     # Shared models/contracts
└── tests/                         # Test projects
    └── HarriTenantApp.Server.Tests/ # Server unit tests
```

### Project Details

#### HarriTenantApp.Server (Blazor Server Application)
- **Framework:** .NET 8.0
- **Packages:**
  - Microsoft.EntityFrameworkCore.SqlServer (9.0.5)
  - Microsoft.EntityFrameworkCore.Tools (9.0.5)
- **References:** HarriTenantApp.Shared
- **Purpose:** Single executable Blazor Server app for CRUD operations on HarriTenant table
- **Architecture:** Direct database access, no separate API layer
- **Deployment:** Can be published as single file executable

#### HarriTenantApp.Shared (Class Library)
- **Framework:** .NET 8.0
- **Purpose:** Shared models, DTOs, and validation attributes

#### Test Projects
- **HarriTenantApp.Server.Tests:** xUnit tests for Server project
- **Framework:** .NET 8.0

## Architecture Decisions

### Technology Stack
- **Application:** Blazor Server (.NET 8.0)
- **Database:** SQL Server (local development)
- **ORM:** Entity Framework Core (Database-First approach)
- **Logging:** Built-in .NET logging (no external framework)
- **Testing:** xUnit
- **DI Container:** Microsoft.Extensions.DependencyInjection (built-in)
- **Architecture:** Single executable with direct database access

### Project References
- Server → Shared (for models/DTOs)
- Server.Tests → Server + Shared

## Task Status
- ✅ **Task 1:** Project Setup and Structure (COMPLETED - December 19, 2024)
  - **RESTRUCTURED:** Changed from Blazor WebAssembly + Web API to Blazor Server architecture
  - Solution created with Blazor Server project structure
  - Deleted API and Client projects, created Server project
  - NuGet packages installed (Entity Framework Core 9.0.5)
  - Project references configured correctly (Server → Shared)
  - Configuration files set up (appsettings.json with correct connection string)
  - .gitignore file exists and configured
  - Solution builds successfully without errors
  - **Architecture:** Single executable Blazor Server for future web portability
- ✅ **Task 2:** Database Integration and Models (COMPLETED - June 5, 2025)
  - **Database Connection:** Successfully configured and tested with working connection string
  - **EF Core Models:** Created HarriTenant.cs model with proper data annotations and schema mapping
  - **DbContext:** Created ApplicationDbContext.cs with proper Entity Framework configuration
  - **Dependency Injection:** Configured DbContext and services in Program.cs
  - **Database Connectivity:** VERIFIED - Application successfully connects and queries HarriTenant table
  - **Test Infrastructure:** Created DatabaseTestService and test page for validation
  - **Connection String:** data source=.;initial catalog=dbNServiceBus_Employment_Config;integrated security=true;enlist=false;TrustServerCertificate=true;
  - **Model Validation:** All HarriTenant fields properly mapped and accessible via Entity Framework
  - **Files Created:** Models/HarriTenant.cs, Data/ApplicationDbContext.cs, Services/DatabaseTestService.cs, Pages/DatabaseTest.razor
- ✅ **Task 3:** Service Layer and Repository Development (COMPLETED - June 5, 2025)
  - **Repository Pattern:** Created IHarriTenantRepository interface and HarriTenantRepository implementation
  - **Service Layer:** Created IHarriTenantService interface and HarriTenantService implementation
  - **Error Handling:** Implemented ServiceResult pattern with comprehensive error management
  - **Business Rules:** Added validation for URLs, client names, secrets, and duplicate prevention
  - **Dependency Injection:** Configured repository and service registration in Program.cs
  - **Test Infrastructure:** Created ServiceLayerTestService with comprehensive validation
  - **Verification:** All service layer tests passing successfully
  - **Files Created:** Repositories/IHarriTenantRepository.cs, Repositories/HarriTenantRepository.cs, Services/IHarriTenantService.cs, Services/HarriTenantService.cs, Services/ServiceResult.cs, Services/ServiceLayerTestService.cs, Pages/ServiceLayerTest.razor
- ❌ **Task 4:** Blazor Server UI Development (NOT STARTED)
- ❌ **Task 5:** Styling and Usability (NOT STARTED)
- ❌ **Task 6:** Testing and Quality Assurance (NOT STARTED)

## Configuration Notes

### Database Connection
- **WORKING Connection String:** data source=.;initial catalog=dbNServiceBus_Employment_Config;integrated security=true;enlist=false;TrustServerCertificate=true;
- **Previous (Non-working):** Server=(localdb);Database=dbNServiceBus_Employment_Config;Trusted_Connection=true;MultipleActiveResultSets=true
- Database-first approach implemented for existing HarriTenant table
- **Database:** dbNServiceBus_Employment_Config (existing database, do not modify schema)
- **Table:** [dbo].[HarriTenant] with TenantId (PK), Client, Secret, TokenUrl, BaseUrl, Name, IsCorporateTenant, IsActive
- **Validation:** All fields required for application (even if nullable in DB)

### Entity Framework Configuration
- **DbContext:** ApplicationDbContext configured with SQL Server provider
- **Model:** HarriTenant entity with proper data annotations and fluent API configuration
- **Dependency Injection:** DbContext registered as scoped service in Program.cs
- **Database Connectivity:** Successfully tested and verified working

### Nullable Reference Types
- Enabled across all projects (Nullable enable)

### Coding Standards Applied
- Allman-style braces
- PascalCase for public members
- camelCase for parameters/locals
- XML documentation for public APIs
- Explicit access modifiers
- .NET 8.0 targeting

## Known Issues and Solutions

### Database Connection Issues (RESOLVED)
- **Issue:** SSL certificate trust issues with SQL Server connection
- **Solution:** Added TrustServerCertificate=true; to connection string
- **Issue:** LocalDB instance naming problems
- **Solution:** Changed from (localdb)\\MSSQLLocalDB to data source=.

## Development Notes

### Entity Framework Database-First Approach
- Models generated manually based on existing database schema
- ApplicationDbContext configured with proper entity mappings
- Database connectivity verified through test page at /database-test
- All CRUD operations ready for implementation in service layer
