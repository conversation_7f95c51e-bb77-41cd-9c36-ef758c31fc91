namespace HarriTenantApp.Server.Services;

/// <summary>
/// Represents the result of a service operation.
/// </summary>
public class ServiceResult
{
    public bool IsSuccess { get; protected set; }
    public string? ErrorMessage { get; protected set; }
    public List<string> ValidationErrors { get; protected set; } = new();

    protected ServiceResult(bool isSuccess, string? errorMessage = null)
    {
        IsSuccess = isSuccess;
        ErrorMessage = errorMessage;
    }

    public static ServiceResult Success()
    {
        return new ServiceResult(true);
    }

    public static ServiceResult Failure(string errorMessage)
    {
        return new ServiceResult(false, errorMessage);
    }

    public static ServiceResult ValidationFailure(IEnumerable<string> validationErrors)
    {
        var result = new ServiceResult(false, "Validation failed");
        result.ValidationErrors.AddRange(validationErrors);
        return result;
    }

    public static ServiceResult ValidationFailure(string validationError)
    {
        var result = new ServiceResult(false, "Validation failed");
        result.ValidationErrors.Add(validationError);
        return result;
    }
}

/// <summary>
/// Represents the result of a service operation with a return value.
/// </summary>
/// <typeparam name="T">The type of the return value.</typeparam>
public class ServiceResult<T> : ServiceResult
{
    public T? Data { get; private set; }

    private ServiceResult(bool isSuccess, T? data = default, string? errorMessage = null)
        : base(isSuccess, errorMessage)
    {
        Data = data;
    }

    public static ServiceResult<T> Success(T data)
    {
        return new ServiceResult<T>(true, data);
    }

    public static new ServiceResult<T> Failure(string errorMessage)
    {
        return new ServiceResult<T>(false, default, errorMessage);
    }

    public static new ServiceResult<T> ValidationFailure(IEnumerable<string> validationErrors)
    {
        var result = new ServiceResult<T>(false, default, "Validation failed");
        result.ValidationErrors.AddRange(validationErrors);
        return result;
    }

    public static new ServiceResult<T> ValidationFailure(string validationError)
    {
        var result = new ServiceResult<T>(false, default, "Validation failed");
        result.ValidationErrors.Add(validationError);
        return result;
    }
}

/// <summary>
/// Custom exception for business logic errors.
/// </summary>
public class BusinessLogicException : Exception
{
    public BusinessLogicException(string message) : base(message)
    {
    }

    public BusinessLogicException(string message, Exception innerException) : base(message, innerException)
    {
    }
}

/// <summary>
/// Custom exception for validation errors.
/// </summary>
public class ValidationException : Exception
{
    public List<string> ValidationErrors { get; }

    public ValidationException(string message) : base(message)
    {
        ValidationErrors = new List<string> { message };
    }

    public ValidationException(IEnumerable<string> validationErrors) : base("Validation failed")
    {
        ValidationErrors = validationErrors.ToList();
    }
}
