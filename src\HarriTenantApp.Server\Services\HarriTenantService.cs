using System.ComponentModel.DataAnnotations;
using HarriTenantApp.Server.Models;
using HarriTenantApp.Server.Repositories;

namespace HarriTenantApp.Server.Services;

/// <summary>
/// Service implementation for HarriTenant business logic operations.
/// </summary>
public class HarriTenantService : IHarriTenantService
{
    private readonly IHarriTenantRepository _repository;
    private readonly ILogger<HarriTenantService> _logger;

    public HarriTenantService(IHarriTenantRepository repository, ILogger<HarriTenantService> logger)
    {
        _repository = repository ?? throw new ArgumentNullException(nameof(repository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <inheritdoc />
    public async Task<ServiceResult<IEnumerable<HarriTenant>>> GetAllTenantsAsync()
    {
        try
        {
            _logger.LogDebug("Getting all tenants");
            var tenants = await _repository.GetAllAsync();
            return ServiceResult<IEnumerable<HarriTenant>>.Success(tenants);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all tenants");
            return ServiceResult<IEnumerable<HarriTenant>>.Failure("An error occurred while retrieving tenants.");
        }
    }

    /// <inheritdoc />
    public async Task<ServiceResult<HarriTenant>> GetTenantByIdAsync(Guid tenantId)
    {
        try
        {
            if (tenantId == Guid.Empty)
            {
                return ServiceResult<HarriTenant>.ValidationFailure("Tenant ID cannot be empty.");
            }

            _logger.LogDebug("Getting tenant by ID: {TenantId}", tenantId);
            var tenant = await _repository.GetByIdAsync(tenantId);
            
            if (tenant == null)
            {
                return ServiceResult<HarriTenant>.Failure("Tenant not found.");
            }

            return ServiceResult<HarriTenant>.Success(tenant);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant by ID: {TenantId}", tenantId);
            return ServiceResult<HarriTenant>.Failure("An error occurred while retrieving the tenant.");
        }
    }

    /// <inheritdoc />
    public async Task<ServiceResult<IEnumerable<HarriTenant>>> GetTenantsByClientAsync(string client)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(client))
            {
                return ServiceResult<IEnumerable<HarriTenant>>.ValidationFailure("Client name cannot be empty.");
            }

            _logger.LogDebug("Getting tenants by client: {Client}", client);
            var tenants = await _repository.GetByClientAsync(client);
            return ServiceResult<IEnumerable<HarriTenant>>.Success(tenants);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenants by client: {Client}", client);
            return ServiceResult<IEnumerable<HarriTenant>>.Failure("An error occurred while retrieving tenants by client.");
        }
    }

    /// <inheritdoc />
    public async Task<ServiceResult<IEnumerable<HarriTenant>>> GetActiveTenantsAsync()
    {
        try
        {
            _logger.LogDebug("Getting active tenants");
            var tenants = await _repository.GetActiveTenantsAsync();
            return ServiceResult<IEnumerable<HarriTenant>>.Success(tenants);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active tenants");
            return ServiceResult<IEnumerable<HarriTenant>>.Failure("An error occurred while retrieving active tenants.");
        }
    }

    /// <inheritdoc />
    public async Task<ServiceResult<HarriTenant>> CreateTenantAsync(HarriTenant tenant)
    {
        try
        {
            if (tenant == null)
            {
                return ServiceResult<HarriTenant>.ValidationFailure("Tenant cannot be null.");
            }

            // Validate the tenant
            var validationResult = await ValidateTenantAsync(tenant, isUpdate: false);
            if (!validationResult.IsSuccess)
            {
                return ServiceResult<HarriTenant>.ValidationFailure(validationResult.ValidationErrors);
            }

            // Check for duplicate client name
            if (await _repository.ClientExistsAsync(tenant.Client))
            {
                return ServiceResult<HarriTenant>.ValidationFailure("A tenant with this client name already exists.");
            }

            _logger.LogDebug("Creating tenant for client: {Client}", tenant.Client);
            var createdTenant = await _repository.CreateAsync(tenant);
            
            _logger.LogInformation("Successfully created tenant with ID: {TenantId}", createdTenant.TenantId);
            return ServiceResult<HarriTenant>.Success(createdTenant);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating tenant for client: {Client}", tenant?.Client);
            return ServiceResult<HarriTenant>.Failure("An error occurred while creating the tenant.");
        }
    }

    /// <inheritdoc />
    public async Task<ServiceResult<HarriTenant>> UpdateTenantAsync(HarriTenant tenant)
    {
        try
        {
            if (tenant == null)
            {
                return ServiceResult<HarriTenant>.ValidationFailure("Tenant cannot be null.");
            }

            // Check if tenant exists
            if (!await _repository.ExistsAsync(tenant.TenantId))
            {
                return ServiceResult<HarriTenant>.Failure("Tenant not found.");
            }

            // Validate the tenant
            var validationResult = await ValidateTenantAsync(tenant, isUpdate: true);
            if (!validationResult.IsSuccess)
            {
                return ServiceResult<HarriTenant>.ValidationFailure(validationResult.ValidationErrors);
            }

            // Check for duplicate client name (excluding current tenant)
            var existingTenants = await _repository.GetByClientAsync(tenant.Client);
            if (existingTenants.Any(t => t.TenantId != tenant.TenantId))
            {
                return ServiceResult<HarriTenant>.ValidationFailure("A tenant with this client name already exists.");
            }

            _logger.LogDebug("Updating tenant with ID: {TenantId}", tenant.TenantId);
            var updatedTenant = await _repository.UpdateAsync(tenant);
            
            _logger.LogInformation("Successfully updated tenant with ID: {TenantId}", updatedTenant.TenantId);
            return ServiceResult<HarriTenant>.Success(updatedTenant);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating tenant with ID: {TenantId}", tenant?.TenantId);
            return ServiceResult<HarriTenant>.Failure("An error occurred while updating the tenant.");
        }
    }

    /// <inheritdoc />
    public async Task<ServiceResult> DeleteTenantAsync(Guid tenantId)
    {
        try
        {
            if (tenantId == Guid.Empty)
            {
                return ServiceResult.ValidationFailure("Tenant ID cannot be empty.");
            }

            _logger.LogDebug("Deleting tenant with ID: {TenantId}", tenantId);
            var deleted = await _repository.DeleteAsync(tenantId);
            
            if (!deleted)
            {
                return ServiceResult.Failure("Tenant not found.");
            }

            _logger.LogInformation("Successfully deleted tenant with ID: {TenantId}", tenantId);
            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting tenant with ID: {TenantId}", tenantId);
            return ServiceResult.Failure("An error occurred while deleting the tenant.");
        }
    }

    /// <inheritdoc />
    public async Task<ServiceResult> ValidateTenantAsync(HarriTenant tenant, bool isUpdate = false)
    {
        var validationErrors = new List<string>();

        try
        {
            // Basic null checks
            if (tenant == null)
            {
                return ServiceResult.ValidationFailure("Tenant cannot be null.");
            }

            // Validate using data annotations
            var validationContext = new ValidationContext(tenant);
            var validationResults = new List<ValidationResult>();
            
            if (!Validator.TryValidateObject(tenant, validationContext, validationResults, true))
            {
                validationErrors.AddRange(validationResults.Select(vr => vr.ErrorMessage ?? "Unknown validation error"));
            }

            // Business rule validations
            await ValidateBusinessRulesAsync(tenant, validationErrors, isUpdate);

            if (validationErrors.Any())
            {
                return ServiceResult.ValidationFailure(validationErrors);
            }

            return ServiceResult.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating tenant");
            return ServiceResult.Failure("An error occurred during validation.");
        }
    }

    /// <inheritdoc />
    public async Task<ServiceResult<int>> GetTenantCountAsync()
    {
        try
        {
            _logger.LogDebug("Getting tenant count");
            var count = await _repository.GetCountAsync();
            return ServiceResult<int>.Success(count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting tenant count");
            return ServiceResult<int>.Failure("An error occurred while getting the tenant count.");
        }
    }

    /// <inheritdoc />
    public async Task<ServiceResult<bool>> TenantExistsAsync(Guid tenantId)
    {
        try
        {
            if (tenantId == Guid.Empty)
            {
                return ServiceResult<bool>.ValidationFailure("Tenant ID cannot be empty.");
            }

            var exists = await _repository.ExistsAsync(tenantId);
            return ServiceResult<bool>.Success(exists);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if tenant exists: {TenantId}", tenantId);
            return ServiceResult<bool>.Failure("An error occurred while checking if the tenant exists.");
        }
    }

    /// <summary>
    /// Validates business rules for HarriTenant.
    /// </summary>
    private async Task ValidateBusinessRulesAsync(HarriTenant tenant, List<string> validationErrors, bool isUpdate)
    {
        // URL validation
        if (!string.IsNullOrWhiteSpace(tenant.TokenUrl) && !IsValidUrl(tenant.TokenUrl))
        {
            validationErrors.Add("Token URL must be a valid URL.");
        }

        if (!string.IsNullOrWhiteSpace(tenant.BaseUrl) && !IsValidUrl(tenant.BaseUrl))
        {
            validationErrors.Add("Base URL must be a valid URL.");
        }

        // Client name business rules
        if (!string.IsNullOrWhiteSpace(tenant.Client))
        {
            if (tenant.Client.Length < 2)
            {
                validationErrors.Add("Client name must be at least 2 characters long.");
            }

            if (tenant.Client.Contains("  ")) // Double spaces
            {
                validationErrors.Add("Client name cannot contain consecutive spaces.");
            }
        }

        // Secret validation
        if (!string.IsNullOrWhiteSpace(tenant.Secret) && tenant.Secret.Length < 8)
        {
            validationErrors.Add("Secret must be at least 8 characters long.");
        }

        await Task.CompletedTask; // For async consistency
    }

    /// <summary>
    /// Validates if a string is a valid URL.
    /// </summary>
    private static bool IsValidUrl(string url)
    {
        return Uri.TryCreate(url, UriKind.Absolute, out var result) &&
               (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
    }
}
