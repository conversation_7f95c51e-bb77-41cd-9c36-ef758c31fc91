using Microsoft.EntityFrameworkCore;
using HarriTenantApp.Server.Models;

namespace HarriTenantApp.Server.Data;

public partial class ApplicationDbContext : DbContext
{
    public ApplicationDbContext()
    {
    }

    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<HarriTenant> HarriTenants { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<HarriTenant>(entity =>
        {
            entity.HasKey(e => e.TenantId);

            entity.ToTable("HarriTenant");

            entity.Property(e => e.TenantId)
                .HasDefaultValueSql("(newid())");

            entity.Property(e => e.Client)
                .IsRequired()
                .HasMaxLength(100);

            entity.Property(e => e.Secret)
                .IsRequired()
                .HasMaxLength(100);

            entity.Property(e => e.TokenUrl)
                .IsRequired()
                .HasMaxLength(100);

            entity.Property(e => e.BaseUrl)
                .IsRequired()
                .HasMaxLength(100);

            entity.Property(e => e.Name)
                .HasMaxLength(50);

            entity.Property(e => e.IsCorporateTenant)
                .IsRequired()
                .HasDefaultValue(false);

            entity.Property(e => e.IsActive)
                .HasDefaultValue(false);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
