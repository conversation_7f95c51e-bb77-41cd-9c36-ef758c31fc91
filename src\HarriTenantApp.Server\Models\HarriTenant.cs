using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HarriTenantApp.Server.Models;

[Table("HarriTenant")]
public partial class HarriTenant
{
    [Key]
    [Column("TenantId")]
    public Guid TenantId { get; set; } = Guid.NewGuid();

    [Required]
    [Column("Client")]
    [StringLength(100)]
    public string Client { get; set; } = string.Empty;

    [Required]
    [Column("Secret")]
    [StringLength(100)]
    public string Secret { get; set; } = string.Empty;

    [Required]
    [Column("TokenUrl")]
    [StringLength(100)]
    public string TokenUrl { get; set; } = string.Empty;

    [Required]
    [Column("BaseUrl")]
    [StringLength(100)]
    public string BaseUrl { get; set; } = string.Empty;

    [Column("Name")]
    [StringLength(50)]
    public string? Name { get; set; }

    [Required]
    [Column("IsCorporateTenant")]
    public bool IsCorporateTenant { get; set; } = false;

    [Column("IsActive")]
    public bool? IsActive { get; set; } = false;
}
