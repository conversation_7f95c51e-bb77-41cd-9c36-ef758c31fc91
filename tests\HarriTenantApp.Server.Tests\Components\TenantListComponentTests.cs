using Bunit;
using FluentAssertions;
using HarriTenantApp.Server.Models;
using HarriTenantApp.Server.Pages.Tenants;
using HarriTenantApp.Server.Services;
using Microsoft.Extensions.DependencyInjection;
using Moq;

namespace HarriTenantApp.Server.Tests.Components
{
    public class TenantListComponentTests : TestContext
    {
        private readonly Mock<IHarriTenantService> _mockTenantService;

        public TenantListComponentTests()
        {
            _mockTenantService = new Mock<IHarriTenantService>();
            Services.AddSingleton(_mockTenantService.Object);
        }

        [Fact]
        public void ShouldRenderLoadingState_WhenDataIsLoading()
        {
            // Arrange
            var tcs = new TaskCompletionSource<ServiceResult<IEnumerable<HarriTenant>>>();
            _mockTenantService
                .Setup(x => x.GetAllTenantsAsync())
                .Returns(tcs.Task);

            // Act
            var component = RenderComponent<TenantList>();

            // Assert
            component.Find(".spinner-border").Should().NotBeNull();
            component.Markup.Should().Contain("Loading tenants...");
        }

        [Fact]
        public void ShouldRenderTenantTable_WhenDataProvided()
        {
            // Arrange
            var tenants = new List<HarriTenant>
            {
                new()
                {
                    TenantId = Guid.NewGuid(),
                    Client = "Test Client 1",
                    Name = "Test Name 1",
                    BaseUrl = "https://test1.com",
                    TokenUrl = "https://test1.com/token",
                    Secret = "testsecret1",
                    IsCorporateTenant = true,
                    IsActive = true
                },
                new()
                {
                    TenantId = Guid.NewGuid(),
                    Client = "Test Client 2",
                    Name = "Test Name 2",
                    BaseUrl = "https://test2.com",
                    TokenUrl = "https://test2.com/token",
                    Secret = "testsecret2",
                    IsCorporateTenant = false,
                    IsActive = false
                }
            };

            _mockTenantService
                .Setup(x => x.GetAllTenantsAsync())
                .ReturnsAsync(ServiceResult<IEnumerable<HarriTenant>>.Success(tenants));

            // Act
            var component = RenderComponent<TenantList>();

            // Assert
            component.Find("table").Should().NotBeNull();
            component.Markup.Should().Contain("Test Client 1");
            component.Markup.Should().Contain("Test Client 2");
            component.Markup.Should().Contain("Corporate");
            component.Markup.Should().Contain("Active");
            component.Markup.Should().Contain("Inactive");
        }

        [Fact]
        public void ShouldRenderEmptyState_WhenNoTenantsExist()
        {
            // Arrange
            _mockTenantService
                .Setup(x => x.GetAllTenantsAsync())
                .ReturnsAsync(ServiceResult<IEnumerable<HarriTenant>>.Success(new List<HarriTenant>()));

            // Act
            var component = RenderComponent<TenantList>();

            // Assert
            component.Markup.Should().Contain("No tenants found");
            component.Markup.Should().Contain("Get started by creating your first tenant");
        }

        [Fact]
        public void ShouldRenderErrorState_WhenServiceFails()
        {
            // Arrange
            _mockTenantService
                .Setup(x => x.GetAllTenantsAsync())
                .ReturnsAsync(ServiceResult<IEnumerable<HarriTenant>>.Failure("Database connection failed"));

            // Act
            var component = RenderComponent<TenantList>();

            // Assert
            component.Find(".alert-danger").Should().NotBeNull();
            component.Markup.Should().Contain("Error loading tenants");
            component.Markup.Should().Contain("Database connection failed");
        }

        [Fact]
        public void ShouldShowCreateButton_WhenRendered()
        {
            // Arrange
            _mockTenantService
                .Setup(x => x.GetAllTenantsAsync())
                .ReturnsAsync(ServiceResult<IEnumerable<HarriTenant>>.Success(new List<HarriTenant>()));

            // Act
            var component = RenderComponent<TenantList>();

            // Assert
            var createButton = component.Find("a[href='/tenants/create']");
            createButton.Should().NotBeNull();
            createButton.TextContent.Should().Contain("Create New Tenant");
        }

        [Fact]
        public void ShouldMaskSecrets_ByDefault()
        {
            // Arrange
            var tenants = new List<HarriTenant>
            {
                new()
                {
                    TenantId = Guid.NewGuid(),
                    Client = "Test Client",
                    Secret = "supersecret123",
                    BaseUrl = "https://test.com",
                    TokenUrl = "https://test.com/token",
                    IsCorporateTenant = false,
                    IsActive = true
                }
            };

            _mockTenantService
                .Setup(x => x.GetAllTenantsAsync())
                .ReturnsAsync(ServiceResult<IEnumerable<HarriTenant>>.Success(tenants));

            // Act
            var component = RenderComponent<TenantList>();

            // Assert
            component.Markup.Should().Contain("••••••••");
            component.Markup.Should().NotContain("supersecret123");
        }

        [Fact]
        public void ShouldShowPagination_WhenMultiplePages()
        {
            // Arrange
            var tenants = Enumerable.Range(1, 30)
                .Select(i => new HarriTenant
                {
                    TenantId = Guid.NewGuid(),
                    Client = $"Test Client {i}",
                    BaseUrl = $"https://test{i}.com",
                    TokenUrl = $"https://test{i}.com/token",
                    Secret = $"secret{i}",
                    IsCorporateTenant = false,
                    IsActive = true
                })
                .ToList();

            _mockTenantService
                .Setup(x => x.GetAllTenantsAsync())
                .ReturnsAsync(ServiceResult<IEnumerable<HarriTenant>>.Success(tenants));

            // Act
            var component = RenderComponent<TenantList>();

            // Assert
            component.Find(".pagination").Should().NotBeNull();
            component.Markup.Should().Contain("Previous");
            component.Markup.Should().Contain("Next");
        }

        [Fact]
        public void ShouldShowFilterControls_WhenRendered()
        {
            // Arrange
            _mockTenantService
                .Setup(x => x.GetAllTenantsAsync())
                .ReturnsAsync(ServiceResult<IEnumerable<HarriTenant>>.Success(new List<HarriTenant>()));

            // Act
            var component = RenderComponent<TenantList>();

            // Assert
            component.Find("input[placeholder*='Search by client name']").Should().NotBeNull();
            component.Find("select").Should().NotBeNull(); // Status filter
            component.Markup.Should().Contain("All Tenants");
            component.Markup.Should().Contain("Active Only");
            component.Markup.Should().Contain("Inactive Only");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _mockTenantService?.Reset();
            }
            base.Dispose(disposing);
        }
    }
}
