@page "/tenants/create"
@using HarriTenantApp.Server.Services
@using HarriTenantApp.Server.Models
@using System.ComponentModel.DataAnnotations
@inject IHarriTenantService TenantService
@inject NavigationManager Navigation

<PageTitle>Create New Tenant</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Create New Tenant</h1>
    <a href="/tenants" class="btn btn-outline-secondary">
        <i class="oi oi-arrow-left"></i> Back to List
    </a>
</div>

@if (!string.IsNullOrEmpty(alertMessage))
{
    <div class="alert @alertClass alert-dismissible fade show" role="alert">
        @alertMessage
        <button type="button" class="btn-close" @onclick="ClearAlert"></button>
    </div>
}

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Tenant Information</h5>
            </div>
            <div class="card-body">
                <EditForm Model="@tenant" OnValidSubmit="@HandleValidSubmit">
                    <DataAnnotationsValidator />
                    
                    <div class="row g-3">
                        <!-- Client Name -->
                        <div class="col-md-6">
                            <label for="client" class="form-label">Client Name <span class="text-danger">*</span></label>
                            <InputText id="client" class="form-control" @bind-Value="tenant.Client" placeholder="Enter client name" />
                            <ValidationMessage For="@(() => tenant.Client)" class="text-danger" />
                        </div>

                        <!-- Display Name -->
                        <div class="col-md-6">
                            <label for="name" class="form-label">Display Name</label>
                            <InputText id="name" class="form-control" @bind-Value="tenant.Name" placeholder="Enter display name (optional)" />
                            <ValidationMessage For="@(() => tenant.Name)" class="text-danger" />
                        </div>

                        <!-- Base URL -->
                        <div class="col-12">
                            <label for="baseUrl" class="form-label">Base URL <span class="text-danger">*</span></label>
                            <InputText id="baseUrl" class="form-control" @bind-Value="tenant.BaseUrl" placeholder="https://example.com" />
                            <ValidationMessage For="@(() => tenant.BaseUrl)" class="text-danger" />
                            <div class="form-text">The main URL for this tenant's application.</div>
                        </div>

                        <!-- Token URL -->
                        <div class="col-12">
                            <label for="tokenUrl" class="form-label">Token URL <span class="text-danger">*</span></label>
                            <InputText id="tokenUrl" class="form-control" @bind-Value="tenant.TokenUrl" placeholder="https://example.com/token" />
                            <ValidationMessage For="@(() => tenant.TokenUrl)" class="text-danger" />
                            <div class="form-text">The URL used for authentication token requests.</div>
                        </div>

                        <!-- Secret -->
                        <div class="col-12">
                            <label for="secret" class="form-label">Secret <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="@(showSecret ? "text" : "password")" 
                                       id="secret" 
                                       class="form-control" 
                                       @bind="tenant.Secret" 
                                       placeholder="Enter secret key" />
                                <button type="button" class="btn btn-outline-secondary" @onclick="ToggleSecretVisibility">
                                    <i class="oi oi-@(showSecret ? "eye-closed" : "eye")"></i>
                                </button>
                            </div>
                            <ValidationMessage For="@(() => tenant.Secret)" class="text-danger" />
                            <div class="form-text">The secret key used for authentication. Must be at least 8 characters.</div>
                        </div>

                        <!-- Corporate Tenant -->
                        <div class="col-md-6">
                            <div class="form-check">
                                <InputCheckbox id="isCorporate" class="form-check-input" @bind-Value="tenant.IsCorporateTenant" />
                                <label class="form-check-label" for="isCorporate">
                                    Corporate Tenant
                                </label>
                            </div>
                            <div class="form-text">Check if this is a corporate tenant with special privileges.</div>
                        </div>

                        <!-- Active Status -->
                        <div class="col-md-6">
                            <div class="form-check">
                                <InputCheckbox id="isActive" class="form-check-input" @bind-Value="isActiveChecked" />
                                <label class="form-check-label" for="isActive">
                                    Active
                                </label>
                            </div>
                            <div class="form-text">Check to activate this tenant immediately.</div>
                        </div>
                    </div>

                    <ValidationSummary class="text-danger mt-3" />

                    <div class="d-flex justify-content-between mt-4">
                        <a href="/tenants" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary" disabled="@isSubmitting">
                            @if (isSubmitting)
                            {
                                <span class="spinner-border spinner-border-sm me-2"></span>
                            }
                            <i class="oi oi-check"></i> Create Tenant
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Help & Guidelines</h6>
            </div>
            <div class="card-body">
                <h6>Required Fields</h6>
                <ul class="small">
                    <li><strong>Client Name:</strong> Unique identifier for the tenant</li>
                    <li><strong>Base URL:</strong> Main application URL</li>
                    <li><strong>Token URL:</strong> Authentication endpoint</li>
                    <li><strong>Secret:</strong> Authentication key (min 8 chars)</li>
                </ul>
                
                <h6 class="mt-3">Validation Rules</h6>
                <ul class="small">
                    <li>Client name must be at least 2 characters</li>
                    <li>URLs must be valid HTTP/HTTPS addresses</li>
                    <li>Secret must be at least 8 characters long</li>
                    <li>Client names must be unique</li>
                </ul>
                
                <h6 class="mt-3">Tips</h6>
                <ul class="small">
                    <li>Use descriptive client names for easy identification</li>
                    <li>Test URLs before saving to ensure they're accessible</li>
                    <li>Store secrets securely and change them regularly</li>
                </ul>
            </div>
        </div>
    </div>
</div>

@code {
    private HarriTenant tenant = new();
    private bool showSecret = false;
    private bool isSubmitting = false;
    private bool isActiveChecked = false;
    
    private string alertMessage = string.Empty;
    private string alertClass = string.Empty;

    protected override void OnInitialized()
    {
        // Initialize with default values
        tenant.TenantId = Guid.NewGuid();
        tenant.IsCorporateTenant = false;
        tenant.IsActive = null; // Will be set based on checkbox
    }

    private void ToggleSecretVisibility()
    {
        showSecret = !showSecret;
    }

    private async Task HandleValidSubmit()
    {
        isSubmitting = true;
        ClearAlert();

        try
        {
            // Set IsActive based on checkbox
            tenant.IsActive = isActiveChecked;

            var result = await TenantService.CreateTenantAsync(tenant);
            
            if (result.IsSuccess)
            {
                // Navigate back to list with success message
                Navigation.NavigateTo($"/tenants?created={Uri.EscapeDataString(tenant.Client)}");
            }
            else
            {
                if (result.ValidationErrors.Any())
                {
                    ShowAlert("Validation errors: " + string.Join(", ", result.ValidationErrors), "alert-danger");
                }
                else
                {
                    ShowAlert("Error creating tenant: " + (result.ErrorMessage ?? "Unknown error"), "alert-danger");
                }
            }
        }
        catch (Exception ex)
        {
            ShowAlert("Error creating tenant: " + ex.Message, "alert-danger");
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private void ShowAlert(string message, string cssClass)
    {
        alertMessage = message;
        alertClass = cssClass;
        StateHasChanged();
    }

    private void ClearAlert()
    {
        alertMessage = string.Empty;
        alertClass = string.Empty;
    }
}
