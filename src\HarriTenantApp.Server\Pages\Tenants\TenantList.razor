@page "/tenants"
@using HarriTenantApp.Server.Services
@using HarriTenantApp.Server.Models
@inject IHarriTenantService TenantService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<PageTitle>Tenant Management</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Tenant Management</h1>
    <a href="/tenants/create" class="btn btn-primary">
        <i class="oi oi-plus"></i> Create New Tenant
    </a>
</div>

@if (!string.IsNullOrEmpty(alertMessage))
{
    <div class="alert @alertClass alert-dismissible fade show" role="alert">
        @alertMessage
        <button type="button" class="btn-close" @onclick="ClearAlert"></button>
    </div>
}

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Filters</h5>
    </div>
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-4">
                <label for="clientSearch" class="form-label">Client Name</label>
                <input type="text" class="form-control" id="clientSearch" @bind="clientSearchTerm" @bind:event="oninput" @onkeyup="OnSearchKeyUp" placeholder="Search by client name..." />
            </div>
            <div class="col-md-3">
                <label for="activeFilter" class="form-label">Status</label>
                <select class="form-select" id="activeFilter" @bind="activeFilter" @bind:after="OnFilterChanged">
                    <option value="">All Tenants</option>
                    <option value="true">Active Only</option>
                    <option value="false">Inactive Only</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="pageSize" class="form-label">Records per page</label>
                <select class="form-select" id="pageSize" @bind="pageSize" @bind:after="OnPageSizeChanged">
                    <option value="25">25</option>
                    <option value="50">50</option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="button" class="btn btn-outline-secondary" @onclick="ClearFilters">
                    <i class="oi oi-x"></i> Clear
                </button>
            </div>
        </div>
    </div>
</div>

@if (loading)
{
    <div class="text-center">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2">Loading tenants...</p>
    </div>
}
else if (filteredTenants.Any())
{
    <!-- Results Summary -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <span class="text-muted">
            Showing @Math.Min(pageSize, filteredTenants.Count() - (currentPage - 1) * pageSize) of @filteredTenants.Count() tenants
            @if (totalTenants != filteredTenants.Count())
            {
                <text>(filtered from @totalTenants total)</text>
            }
        </span>
        <div>
            <button class="btn btn-outline-secondary btn-sm me-2" @onclick="() => ToggleSort()" title="Sort by Client Name">
                <i class="oi oi-sort-@(sortAscending ? "ascending" : "descending")"></i> Client Name
            </button>
        </div>
    </div>

    <!-- Tenants Table -->
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead class="table-dark">
                <tr>
                    <th>Client</th>
                    <th>Name</th>
                    <th>Base URL</th>
                    <th>Token URL</th>
                    <th>Secret</th>
                    <th>Corporate</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var tenant in pagedTenants)
                {
                    <tr>
                        <td>
                            <strong>@tenant.Client</strong>
                        </td>
                        <td>@(tenant.Name ?? "N/A")</td>
                        <td>
                            @if (!string.IsNullOrEmpty(tenant.BaseUrl))
                            {
                                <a href="@tenant.BaseUrl" target="_blank" class="text-decoration-none">
                                    @(tenant.BaseUrl.Length > 30 ? tenant.BaseUrl.Substring(0, 30) + "..." : tenant.BaseUrl)
                                    <i class="oi oi-external-link" style="font-size: 0.8em;"></i>
                                </a>
                            }
                            else
                            {
                                <span class="text-muted">N/A</span>
                            }
                        </td>
                        <td>
                            @if (!string.IsNullOrEmpty(tenant.TokenUrl))
                            {
                                <span class="text-muted">@(tenant.TokenUrl.Length > 30 ? tenant.TokenUrl.Substring(0, 30) + "..." : tenant.TokenUrl)</span>
                            }
                            else
                            {
                                <span class="text-muted">N/A</span>
                            }
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <span class="me-2">
                                    @if (visibleSecrets.Contains(tenant.TenantId))
                                    {
                                        <code>@tenant.Secret</code>
                                    }
                                    else
                                    {
                                        <span>••••••••</span>
                                    }
                                </span>
                                <button type="button" class="btn btn-outline-secondary btn-sm" @onclick="() => ToggleSecretVisibility(tenant.TenantId)" title="@(visibleSecrets.Contains(tenant.TenantId) ? "Hide" : "Show") Secret">
                                    <i class="oi oi-@(visibleSecrets.Contains(tenant.TenantId) ? "eye-closed" : "eye")"></i>
                                </button>
                            </div>
                        </td>
                        <td>
                            @if (tenant.IsCorporateTenant)
                            {
                                <span class="badge bg-info">Corporate</span>
                            }
                            else
                            {
                                <span class="badge bg-secondary">Standard</span>
                            }
                        </td>
                        <td>
                            @if (tenant.IsActive == true)
                            {
                                <span class="badge bg-success">Active</span>
                            }
                            else
                            {
                                <span class="badge bg-warning text-dark">Inactive</span>
                            }
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="/tenants/edit/@tenant.TenantId" class="btn btn-outline-primary btn-sm" title="Edit">
                                    <i class="oi oi-pencil"></i>
                                </a>
                                <button type="button" class="btn btn-outline-danger btn-sm" @onclick="() => ShowDeleteConfirmation(tenant)" title="Delete">
                                    <i class="oi oi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    @if (totalPages > 1)
    {
        <nav aria-label="Tenant pagination">
            <ul class="pagination justify-content-center">
                <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                    <button class="page-link" @onclick="() => GoToPage(currentPage - 1)" disabled="@(currentPage == 1)">Previous</button>
                </li>
                
                @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                {
                    <li class="page-item @(i == currentPage ? "active" : "")">
                        <button class="page-link" @onclick="() => GoToPage(i)">@i</button>
                    </li>
                }
                
                <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                    <button class="page-link" @onclick="() => GoToPage(currentPage + 1)" disabled="@(currentPage == totalPages)">Next</button>
                </li>
            </ul>
        </nav>
    }
}
else if (!loading)
{
    <div class="text-center py-5">
        <i class="oi oi-info" style="font-size: 3rem; color: #6c757d;"></i>
        <h4 class="mt-3">No tenants found</h4>
        <p class="text-muted">
            @if (HasActiveFilters())
            {
                <text>No tenants match your current filters. Try adjusting your search criteria.</text>
            }
            else
            {
                <text>Get started by creating your first tenant.</text>
            }
        </p>
        @if (HasActiveFilters())
        {
            <button type="button" class="btn btn-outline-secondary me-2" @onclick="ClearFilters">Clear Filters</button>
        }
        <a href="/tenants/create" class="btn btn-primary">Create New Tenant</a>
    </div>
}

<!-- Delete Confirmation Modal -->
@if (tenantToDelete != null)
{
    <div class="modal fade show" style="display: block;" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" @onclick="CancelDelete"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this tenant?</p>
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">@tenantToDelete.Client</h6>
                            <p class="card-text">
                                <strong>Name:</strong> @(tenantToDelete.Name ?? "N/A")<br/>
                                <strong>Base URL:</strong> @(tenantToDelete.BaseUrl ?? "N/A")<br/>
                                <strong>Status:</strong> @(tenantToDelete.IsActive == true ? "Active" : "Inactive")
                            </p>
                        </div>
                    </div>
                    <div class="alert alert-warning mt-3">
                        <i class="oi oi-warning"></i> This action cannot be undone.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CancelDelete">Cancel</button>
                    <button type="button" class="btn btn-danger" @onclick="ConfirmDelete" disabled="@deletingTenant">
                        @if (deletingTenant)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        Delete Tenant
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
}

@code {
    private List<HarriTenant> allTenants = new();
    private IEnumerable<HarriTenant> filteredTenants = new List<HarriTenant>();
    private IEnumerable<HarriTenant> pagedTenants = new List<HarriTenant>();
    private HashSet<Guid> visibleSecrets = new();

    private bool loading = true;
    private string clientSearchTerm = string.Empty;
    private string activeFilter = string.Empty;
    private int pageSize = 25;
    private int currentPage = 1;
    private int totalPages = 1;
    private int totalTenants = 0;
    private bool sortAscending = true;

    private string alertMessage = string.Empty;
    private string alertClass = string.Empty;

    private HarriTenant? tenantToDelete;
    private bool deletingTenant = false;

    private Timer? searchTimer;

    protected override async Task OnInitializedAsync()
    {
        await LoadTenants();
        CheckForSuccessMessages();
    }

    private void CheckForSuccessMessages()
    {
        var uri = new Uri(Navigation.Uri);
        var query = Microsoft.AspNetCore.WebUtilities.QueryHelpers.ParseQuery(uri.Query);

        if (query.TryGetValue("created", out var createdClient))
        {
            ShowAlert($"Tenant '{createdClient}' created successfully.", "alert-success");
        }
        else if (query.TryGetValue("updated", out var updatedClient))
        {
            ShowAlert($"Tenant '{updatedClient}' updated successfully.", "alert-success");
        }
    }

    private async Task LoadTenants()
    {
        loading = true;
        try
        {
            var result = await TenantService.GetAllTenantsAsync();
            if (result.IsSuccess && result.Data != null)
            {
                allTenants = result.Data.ToList();
                totalTenants = allTenants.Count;
                ApplyFiltersAndPaging();
                ClearAlert();
            }
            else
            {
                ShowAlert("Error loading tenants: " + (result.ErrorMessage ?? "Unknown error"), "alert-danger");
            }
        }
        catch (Exception ex)
        {
            ShowAlert("Error loading tenants: " + ex.Message, "alert-danger");
        }
        finally
        {
            loading = false;
        }
    }

    private void ApplyFiltersAndPaging()
    {
        // Apply filters
        filteredTenants = allTenants.AsEnumerable();

        // Client name filter
        if (!string.IsNullOrWhiteSpace(clientSearchTerm))
        {
            filteredTenants = filteredTenants.Where(t =>
                t.Client.Contains(clientSearchTerm, StringComparison.OrdinalIgnoreCase) ||
                (t.Name?.Contains(clientSearchTerm, StringComparison.OrdinalIgnoreCase) ?? false));
        }

        // Active status filter
        if (!string.IsNullOrEmpty(activeFilter))
        {
            bool isActive = bool.Parse(activeFilter);
            filteredTenants = filteredTenants.Where(t => t.IsActive == isActive);
        }

        // Apply sorting
        filteredTenants = sortAscending
            ? filteredTenants.OrderBy(t => t.Client)
            : filteredTenants.OrderByDescending(t => t.Client);

        // Calculate pagination
        totalPages = (int)Math.Ceiling((double)filteredTenants.Count() / pageSize);
        currentPage = Math.Min(currentPage, Math.Max(1, totalPages));

        // Apply paging
        pagedTenants = filteredTenants
            .Skip((currentPage - 1) * pageSize)
            .Take(pageSize);

        StateHasChanged();
    }

    private void OnSearchKeyUp(KeyboardEventArgs e)
    {
        searchTimer?.Dispose();
        searchTimer = new Timer(async _ =>
        {
            await InvokeAsync(() =>
            {
                currentPage = 1;
                ApplyFiltersAndPaging();
            });
        }, null, 300, Timeout.Infinite);
    }

    private void OnFilterChanged()
    {
        currentPage = 1;
        ApplyFiltersAndPaging();
    }

    private void OnPageSizeChanged()
    {
        currentPage = 1;
        ApplyFiltersAndPaging();
    }

    private void ToggleSort()
    {
        sortAscending = !sortAscending;
        ApplyFiltersAndPaging();
    }

    private void GoToPage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            ApplyFiltersAndPaging();
        }
    }

    private void ClearFilters()
    {
        clientSearchTerm = string.Empty;
        activeFilter = string.Empty;
        currentPage = 1;
        ApplyFiltersAndPaging();
    }

    private bool HasActiveFilters()
    {
        return !string.IsNullOrWhiteSpace(clientSearchTerm) || !string.IsNullOrEmpty(activeFilter);
    }

    private void ToggleSecretVisibility(Guid tenantId)
    {
        if (visibleSecrets.Contains(tenantId))
        {
            visibleSecrets.Remove(tenantId);
        }
        else
        {
            visibleSecrets.Add(tenantId);
        }
    }

    private void ShowDeleteConfirmation(HarriTenant tenant)
    {
        tenantToDelete = tenant;
    }

    private void CancelDelete()
    {
        tenantToDelete = null;
        deletingTenant = false;
    }

    private async Task ConfirmDelete()
    {
        if (tenantToDelete == null) return;

        deletingTenant = true;
        try
        {
            var result = await TenantService.DeleteTenantAsync(tenantToDelete.TenantId);
            if (result.IsSuccess)
            {
                ShowAlert($"Tenant '{tenantToDelete.Client}' deleted successfully.", "alert-success");
                await LoadTenants();
            }
            else
            {
                ShowAlert("Error deleting tenant: " + (result.ErrorMessage ?? "Unknown error"), "alert-danger");
            }
        }
        catch (Exception ex)
        {
            ShowAlert("Error deleting tenant: " + ex.Message, "alert-danger");
        }
        finally
        {
            tenantToDelete = null;
            deletingTenant = false;
        }
    }

    private void ShowAlert(string message, string cssClass)
    {
        alertMessage = message;
        alertClass = cssClass;
        StateHasChanged();
    }

    private void ClearAlert()
    {
        alertMessage = string.Empty;
        alertClass = string.Empty;
    }

    public void Dispose()
    {
        searchTimer?.Dispose();
    }
}
