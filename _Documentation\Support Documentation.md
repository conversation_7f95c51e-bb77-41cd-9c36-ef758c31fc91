---
purpose: "Comprehensive project documentation for human developers and support teams"
audience: "Human Developers, Support Teams, Stakeholders"
instructions: |
  Document setup procedures, deployment instructions, troubleshooting guides,
  API documentation, and user workflows. Focus on practical, actionable information
  for team members who need to understand, maintain, or support this project.
update_triggers: "When user-facing features change, deployment procedures update, or new setup requirements are added"
---

# Support Documentation

This file provides comprehensive reference information for human developers and support teams working on the HarriTenantApp project.

## Setup and Installation Instructions

### Prerequisites
- Visual Studio 2022 or later (or VS Code with C# extension)
- .NET 8.0 SDK
- SQL Server (LocalDB or full instance)
- Git

### Project Setup
1. **Clone Repository:**
   ```bash
   git clone <repository-url>
   cd HarriTenantApp\HarriTenantGithub
   ```

2. **Restore Dependencies:**
   ```bash
   dotnet restore
   ```

3. **Build Solution:**
   ```bash
   dotnet build
   ```

4. **Verify Setup:**
   All projects should build successfully without errors.

5. **Run Application:**
   ```bash
   cd src/HarriTenantApp.Server
   dotnet run
   ```

6. **Test Database Connection:**
   - Navigate to http://localhost:5097/database-test
   - Verify "Can Connect: Yes" status
   - Check that tenant count and data display correctly

### Project Structure
- **Solution File:** `HarriTenantApp.sln`
- **Server Project:** `src/HarriTenantApp.Server/` - Blazor Server Application
- **Shared Library:** `src/HarriTenantApp.Shared/` - Common models/DTOs
- **Tests:** `tests/HarriTenantApp.Server.Tests/` - xUnit test projects

### Dependencies
- **Entity Framework Core:** 9.0.5 (SQL Server provider)
- **Microsoft.EntityFrameworkCore.Tools:** 9.0.5 (EF Core tooling)
- **xUnit:** Latest (Testing framework)

## Deployment Procedures

*To be documented when deployment configurations are established*

## Troubleshooting Guides

### Common Build Issues
1. **Missing .NET 8.0 SDK:**
   - Download and install from https://dotnet.microsoft.com/download
   - Verify with: `dotnet --version`

2. **Package Restore Failures:**
   - Clear NuGet cache: `dotnet nuget locals all --clear`
   - Restore packages: `dotnet restore`

3. **Project Reference Issues:**
   - Verify all project references are correct in .csproj files
   - Rebuild solution: `dotnet build --no-incremental`

## API Documentation

### Service Layer Interface

#### IHarriTenantService Methods
- **GetAllTenantsAsync()** - Retrieves all tenant records
- **GetTenantByIdAsync(Guid id)** - Retrieves specific tenant by ID
- **CreateTenantAsync(HarriTenant tenant)** - Creates new tenant with validation
- **UpdateTenantAsync(HarriTenant tenant)** - Updates existing tenant
- **DeleteTenantAsync(Guid id)** - Deletes tenant by ID

#### Repository Layer Interface

#### IHarriTenantRepository Methods
- **GetAllAsync()** - Database query for all tenants
- **GetByIdAsync(Guid id)** - Database query for specific tenant
- **CreateAsync(HarriTenant tenant)** - Database insert operation
- **UpdateAsync(HarriTenant tenant)** - Database update operation
- **DeleteAsync(Guid id)** - Database delete operation
- **ExistsAsync(Guid id)** - Check if tenant exists

### Data Models

#### HarriTenant Model Properties
- **TenantId** (Guid) - Primary key, auto-generated
- **Client** (string) - Required, max 100 characters
- **Secret** (string) - Required, min 8 characters
- **TokenUrl** (string) - Required, valid HTTPS URL
- **BaseUrl** (string) - Required, valid HTTPS URL
- **Name** (string) - Optional, max 50 characters
- **IsCorporateTenant** (bool) - Corporate classification flag
- **IsActive** (bool) - Active status flag

### Service Result Pattern
All service methods return ServiceResult<T> with:
- **IsSuccess** (bool) - Operation success indicator
- **Data** (T) - Result data when successful
- **ErrorMessage** (string) - Error description when failed
- **ValidationErrors** (List<string>) - Validation error details

## Configuration Details

### Connection Strings
- **Working Connection String:** `data source=.;initial catalog=dbNServiceBus_Employment_Config;integrated security=true;enlist=false;TrustServerCertificate=true;`
- **Configuration Location:** `src/HarriTenantApp.Server/appsettings.json`
- **Database:** dbNServiceBus_Employment_Config (existing database)
- **Table:** HarriTenant (do not modify schema)

### Database Configuration
- **ORM:** Entity Framework Core (Database-First approach)
- **Model:** HarriTenant.cs with proper data annotations
- **DbContext:** ApplicationDbContext.cs configured with SQL Server provider
- **Test Page:** Available at `/database-test` for connection verification

### Logging Configuration
- **Framework:** Built-in .NET logging (Microsoft.Extensions.Logging)
- **Configuration:** appsettings.json LogLevel section
- **Entity Framework Logging:** Enabled for database command tracking

### Environment Variables
*To be documented as configuration requirements are identified*

## User Guides and Workflows

*To be documented when UI components are implemented*

---

**Last Updated:** June 5, 2025
**Project Status:** Task 2 Complete - Database Integration and Models implemented and tested

### Current Application Status
- ✅ **Task 1:** Project Setup and Structure (COMPLETED)
- ✅ **Task 2:** Database Integration and Models (COMPLETED)
- **Application URL:** http://localhost:5097 or https://localhost:7125
- **Database Test Page:** http://localhost:5097/database-test
- **Database Connection:** Successfully verified and working
