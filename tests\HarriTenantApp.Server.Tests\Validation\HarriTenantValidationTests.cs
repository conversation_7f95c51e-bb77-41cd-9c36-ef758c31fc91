using FluentAssertions;
using HarriTenantApp.Server.Models;
using System.ComponentModel.DataAnnotations;

namespace HarriTenantApp.Server.Tests.Validation
{
    public class HarriTenantValidationTests : TestBase
    {
        [Theory]
        [AutoMoqData]
        public void ShouldPassValidation_WhenAllFieldsAreValid(HarriTenant tenant)
        {
            // Arrange
            tenant.Client = "Valid Client Name";
            tenant.Secret = "ValidSecret123";
            tenant.TokenUrl = "https://valid.com/token";
            tenant.BaseUrl = "https://valid.com";
            tenant.Name = "Valid Display Name";

            // Act
            var validationResults = ValidateModel(tenant);

            // Assert
            validationResults.Should().BeEmpty();
        }

        [Fact]
        public void ShouldFailValidation_WhenClientIsEmpty()
        {
            // Arrange
            var tenant = CreateValidTenant();
            tenant.Client = "";

            // Act
            var validationResults = ValidateModel(tenant);

            // Assert
            validationResults.Should().NotBeEmpty();
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("Client"));
        }

        [Fact]
        public void ShouldFailValidation_WhenClientIsTooLong()
        {
            // Arrange
            var tenant = CreateValidTenant();
            tenant.Client = new string('A', 101); // Exceeds 100 character limit

            // Act
            var validationResults = ValidateModel(tenant);

            // Assert
            validationResults.Should().NotBeEmpty();
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("Client"));
        }

        [Fact]
        public void ShouldFailValidation_WhenSecretIsEmpty()
        {
            // Arrange
            var tenant = CreateValidTenant();
            tenant.Secret = "";

            // Act
            var validationResults = ValidateModel(tenant);

            // Assert
            validationResults.Should().NotBeEmpty();
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("Secret"));
        }

        [Fact]
        public void ShouldFailValidation_WhenSecretIsTooShort()
        {
            // Arrange
            var tenant = CreateValidTenant();
            tenant.Secret = "1234567"; // Less than 8 characters

            // Act
            var validationResults = ValidateModel(tenant);

            // Assert
            validationResults.Should().NotBeEmpty();
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("Secret"));
        }

        [Fact]
        public void ShouldFailValidation_WhenTokenUrlIsEmpty()
        {
            // Arrange
            var tenant = CreateValidTenant();
            tenant.TokenUrl = "";

            // Act
            var validationResults = ValidateModel(tenant);

            // Assert
            validationResults.Should().NotBeEmpty();
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("TokenUrl"));
        }

        [Fact]
        public void ShouldFailValidation_WhenTokenUrlIsInvalid()
        {
            // Arrange
            var tenant = CreateValidTenant();
            tenant.TokenUrl = "not-a-valid-url";

            // Act
            var validationResults = ValidateModel(tenant);

            // Assert
            validationResults.Should().NotBeEmpty();
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("TokenUrl"));
        }

        [Fact]
        public void ShouldFailValidation_WhenBaseUrlIsEmpty()
        {
            // Arrange
            var tenant = CreateValidTenant();
            tenant.BaseUrl = "";

            // Act
            var validationResults = ValidateModel(tenant);

            // Assert
            validationResults.Should().NotBeEmpty();
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("BaseUrl"));
        }

        [Fact]
        public void ShouldFailValidation_WhenBaseUrlIsInvalid()
        {
            // Arrange
            var tenant = CreateValidTenant();
            tenant.BaseUrl = "not-a-valid-url";

            // Act
            var validationResults = ValidateModel(tenant);

            // Assert
            validationResults.Should().NotBeEmpty();
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("BaseUrl"));
        }

        [Fact]
        public void ShouldPassValidation_WhenNameIsNull()
        {
            // Arrange
            var tenant = CreateValidTenant();
            tenant.Name = null; // Name is optional

            // Act
            var validationResults = ValidateModel(tenant);

            // Assert
            validationResults.Should().BeEmpty();
        }

        [Fact]
        public void ShouldFailValidation_WhenNameIsTooLong()
        {
            // Arrange
            var tenant = CreateValidTenant();
            tenant.Name = new string('A', 51); // Exceeds 50 character limit

            // Act
            var validationResults = ValidateModel(tenant);

            // Assert
            validationResults.Should().NotBeEmpty();
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("Name"));
        }

        [Theory]
        [InlineData("https://example.com")]
        [InlineData("http://example.com")]
        [InlineData("https://subdomain.example.com/path")]
        [InlineData("https://example.com:8080/api")]
        public void ShouldPassValidation_WhenUrlsAreValid(string validUrl)
        {
            // Arrange
            var tenant = CreateValidTenant();
            tenant.BaseUrl = validUrl;
            tenant.TokenUrl = validUrl + "/token";

            // Act
            var validationResults = ValidateModel(tenant);

            // Assert
            validationResults.Should().BeEmpty();
        }

        [Theory]
        [InlineData("ftp://example.com")]
        [InlineData("example.com")]
        [InlineData("www.example.com")]
        [InlineData("")]
        [InlineData("   ")]
        public void ShouldFailValidation_WhenUrlsAreInvalid(string invalidUrl)
        {
            // Arrange
            var tenant = CreateValidTenant();
            tenant.BaseUrl = invalidUrl;
            tenant.TokenUrl = invalidUrl;

            // Act
            var validationResults = ValidateModel(tenant);

            // Assert
            validationResults.Should().NotBeEmpty();
        }

        [Fact]
        public void ShouldFailValidation_WhenMultipleFieldsAreInvalid()
        {
            // Arrange
            var tenant = new HarriTenant
            {
                TenantId = Guid.NewGuid(),
                Client = "", // Invalid
                Secret = "123", // Invalid - too short
                TokenUrl = "invalid", // Invalid
                BaseUrl = "invalid", // Invalid
                Name = new string('A', 51), // Invalid - too long
                IsCorporateTenant = false,
                IsActive = true
            };

            // Act
            var validationResults = ValidateModel(tenant);

            // Assert
            validationResults.Should().NotBeEmpty();
            validationResults.Should().HaveCountGreaterThan(1);
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("Client"));
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("Secret"));
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("TokenUrl"));
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("BaseUrl"));
            validationResults.Should().Contain(vr => vr.MemberNames.Contains("Name"));
        }

        private static HarriTenant CreateValidTenant()
        {
            return new HarriTenant
            {
                TenantId = Guid.NewGuid(),
                Client = "Valid Client",
                Secret = "ValidSecret123",
                TokenUrl = "https://example.com/token",
                BaseUrl = "https://example.com",
                Name = "Valid Name",
                IsCorporateTenant = false,
                IsActive = true
            };
        }

        private static List<ValidationResult> ValidateModel(object model)
        {
            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(model);
            Validator.TryValidateObject(model, validationContext, validationResults, true);
            return validationResults;
        }
    }
}
