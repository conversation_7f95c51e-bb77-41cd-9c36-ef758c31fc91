

---
title: "HarriTenant CRUD Application - Product Requirements Document"
description: "Comprehensive requirements and specifications for the HarriTenant CRUD application"
author: "Development Team"
created: "2024-12-19"
updated: "2025-06-04"
version: "2.0"
project: "HarriTenantApp"
audience: "Developers, Project Managers, AI Assistants"
purpose: "Product Requirements Document defining scope, architecture, and technical specifications"
---

# Project Overview

## Project Name: HarriTenantApp

### Purpose
HarriTenantApp is a simple web application for performing Create, Read, Update, and Delete (CRUD) operations on the HarriTenant database table. The application does not include environment lifecycle management, access control, or advanced features. Its sole purpose is to provide a user interface and API for basic CRUD operations on the HarriTenant table.

### Key Features
- Create new HarriTenant records
- View a list of HarriTenant records
- Update existing HarriTenant records
- Delete HarriTenant records

### Technology Stack
- **Frontend/Backend**: Blazor Server (.NET 8.0)
- **Database**: SQL Server (local development)
- **ORM**: Entity Framework Core
- **Dependency Injection**: Microsoft.Extensions.DependencyInjection
- **Testing**: xUnit

### Architecture
The application uses a single-executable Blazor Server architecture:
- **Presentation Layer**: Blazor Server components for UI
- **Business Logic Layer**: Services for validation and business rules
- **Data Access Layer**: Direct Entity Framework Core integration (no separate API layer)

### Error Handling and Validation
- **Client-side Validation**: Form validation using Blazor validation components
- **Server-side Validation**: Model validation attributes and custom validation logic
- **Error Handling**: Try-catch blocks with user-friendly error messages
- **Database Operations**: Direct Entity Framework error handling

### Configuration
- Application settings are managed via `appsettings.json` for local development.
- Database connection string configured for local SQL Server instance.
- Dependency injection is used for service and repository management.

### Local Development
- The application runs as a single executable Blazor Server application.
- Can be published as a single file for easy deployment.
- Database connections target local SQL Server instance.
- No external dependencies or cloud services required.
- Future web server deployment requires minimal changes.

---

*For more details, refer to the How To Guide and Tasks documentation.*
