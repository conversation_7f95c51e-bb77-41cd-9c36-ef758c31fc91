using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace HarriTenantApp.Server.Services
{
    public class ToastService
    {
        public event Action<ToastMessage>? OnToastAdded;
        public event Action<string>? OnToastRemoved;

        private readonly List<ToastMessage> _toasts = new();

        public void ShowSuccess(string message, string? title = null, int duration = 5000)
        {
            ShowToast(ToastType.Success, message, title, duration);
        }

        public void ShowError(string message, string? title = null, int duration = 8000)
        {
            ShowToast(ToastType.Error, message, title, duration);
        }

        public void ShowWarning(string message, string? title = null, int duration = 6000)
        {
            ShowToast(ToastType.Warning, message, title, duration);
        }

        public void ShowInfo(string message, string? title = null, int duration = 5000)
        {
            ShowToast(ToastType.Info, message, title, duration);
        }

        private void ShowToast(ToastType type, string message, string? title, int duration)
        {
            var toast = new ToastMessage
            {
                Id = Guid.NewGuid().ToString(),
                Type = type,
                Title = title,
                Message = message,
                Duration = duration,
                CreatedAt = DateTime.Now
            };

            _toasts.Add(toast);
            OnToastAdded?.Invoke(toast);

            // Auto-remove after duration
            Task.Delay(duration).ContinueWith(_ => RemoveToast(toast.Id));
        }

        public void RemoveToast(string id)
        {
            var toast = _toasts.FirstOrDefault(t => t.Id == id);
            if (toast != null)
            {
                _toasts.Remove(toast);
                OnToastRemoved?.Invoke(id);
            }
        }

        public IEnumerable<ToastMessage> GetToasts() => _toasts.AsReadOnly();
    }

    public class ToastMessage
    {
        public string Id { get; set; } = string.Empty;
        public ToastType Type { get; set; }
        public string? Title { get; set; }
        public string Message { get; set; } = string.Empty;
        public int Duration { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public enum ToastType
    {
        Success,
        Error,
        Warning,
        Info
    }
}
