using HarriTenantApp.Server.Models;

namespace HarriTenantApp.Server.Services;

/// <summary>
/// Service interface for HarriTenant business logic operations.
/// </summary>
public interface IHarriTenantService
{
    /// <summary>
    /// Gets all HarriTenant records.
    /// </summary>
    /// <returns>A service result containing the list of HarriTenant records.</returns>
    Task<ServiceResult<IEnumerable<HarriTenant>>> GetAllTenantsAsync();

    /// <summary>
    /// Gets a specific HarriTenant record by its ID.
    /// </summary>
    /// <param name="tenantId">The unique identifier of the tenant.</param>
    /// <returns>A service result containing the HarriTenant record if found.</returns>
    Task<ServiceResult<HarriTenant>> GetTenantByIdAsync(Guid tenantId);

    /// <summary>
    /// Gets HarriTenant records by client name.
    /// </summary>
    /// <param name="client">The client name to search for.</param>
    /// <returns>A service result containing the list of matching HarriTenant records.</returns>
    Task<ServiceResult<IEnumerable<HarriTenant>>> GetTenantsByClientAsync(string client);

    /// <summary>
    /// Gets active HarriTenant records.
    /// </summary>
    /// <returns>A service result containing the list of active HarriTenant records.</returns>
    Task<ServiceResult<IEnumerable<HarriTenant>>> GetActiveTenantsAsync();

    /// <summary>
    /// Creates a new HarriTenant record with validation and business rules.
    /// </summary>
    /// <param name="tenant">The HarriTenant record to create.</param>
    /// <returns>A service result containing the created HarriTenant record.</returns>
    Task<ServiceResult<HarriTenant>> CreateTenantAsync(HarriTenant tenant);

    /// <summary>
    /// Updates an existing HarriTenant record with validation and business rules.
    /// </summary>
    /// <param name="tenant">The HarriTenant record to update.</param>
    /// <returns>A service result containing the updated HarriTenant record.</returns>
    Task<ServiceResult<HarriTenant>> UpdateTenantAsync(HarriTenant tenant);

    /// <summary>
    /// Deletes a HarriTenant record.
    /// </summary>
    /// <param name="tenantId">The unique identifier of the tenant to delete.</param>
    /// <returns>A service result indicating success or failure.</returns>
    Task<ServiceResult> DeleteTenantAsync(Guid tenantId);

    /// <summary>
    /// Validates a HarriTenant record according to business rules.
    /// </summary>
    /// <param name="tenant">The HarriTenant record to validate.</param>
    /// <param name="isUpdate">Whether this is an update operation (affects validation rules).</param>
    /// <returns>A service result indicating validation success or failure with error details.</returns>
    Task<ServiceResult> ValidateTenantAsync(HarriTenant tenant, bool isUpdate = false);

    /// <summary>
    /// Gets the total count of HarriTenant records.
    /// </summary>
    /// <returns>A service result containing the total count.</returns>
    Task<ServiceResult<int>> GetTenantCountAsync();

    /// <summary>
    /// Checks if a tenant exists with the specified ID.
    /// </summary>
    /// <param name="tenantId">The unique identifier of the tenant.</param>
    /// <returns>A service result indicating whether the tenant exists.</returns>
    Task<ServiceResult<bool>> TenantExistsAsync(Guid tenantId);
}
