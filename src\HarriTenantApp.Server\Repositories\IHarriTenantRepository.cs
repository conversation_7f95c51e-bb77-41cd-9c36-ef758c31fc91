using HarriTenantApp.Server.Models;

namespace HarriTenantApp.Server.Repositories;

/// <summary>
/// Repository interface for HarriTenant data access operations.
/// </summary>
public interface IHarriTenantRepository
{
    /// <summary>
    /// Gets all HarriTenant records from the database.
    /// </summary>
    /// <returns>A list of all HarriTenant records.</returns>
    Task<IEnumerable<HarriTenant>> GetAllAsync();

    /// <summary>
    /// Gets a specific HarriTenant record by its ID.
    /// </summary>
    /// <param name="tenantId">The unique identifier of the tenant.</param>
    /// <returns>The HarriTenant record if found, null otherwise.</returns>
    Task<HarriTenant?> GetByIdAsync(Guid tenantId);

    /// <summary>
    /// Gets HarriTenant records by client name.
    /// </summary>
    /// <param name="client">The client name to search for.</param>
    /// <returns>A list of HarriTenant records matching the client name.</returns>
    Task<IEnumerable<HarriTenant>> GetByClientAsync(string client);

    /// <summary>
    /// Gets active HarriTenant records.
    /// </summary>
    /// <returns>A list of active HarriTenant records.</returns>
    Task<IEnumerable<HarriTenant>> GetActiveTenantsAsync();

    /// <summary>
    /// Creates a new HarriTenant record in the database.
    /// </summary>
    /// <param name="tenant">The HarriTenant record to create.</param>
    /// <returns>The created HarriTenant record with generated ID.</returns>
    Task<HarriTenant> CreateAsync(HarriTenant tenant);

    /// <summary>
    /// Updates an existing HarriTenant record in the database.
    /// </summary>
    /// <param name="tenant">The HarriTenant record to update.</param>
    /// <returns>The updated HarriTenant record.</returns>
    Task<HarriTenant> UpdateAsync(HarriTenant tenant);

    /// <summary>
    /// Deletes a HarriTenant record from the database.
    /// </summary>
    /// <param name="tenantId">The unique identifier of the tenant to delete.</param>
    /// <returns>True if the record was deleted, false if not found.</returns>
    Task<bool> DeleteAsync(Guid tenantId);

    /// <summary>
    /// Checks if a HarriTenant record exists with the specified ID.
    /// </summary>
    /// <param name="tenantId">The unique identifier of the tenant.</param>
    /// <returns>True if the record exists, false otherwise.</returns>
    Task<bool> ExistsAsync(Guid tenantId);

    /// <summary>
    /// Checks if a HarriTenant record exists with the specified client name.
    /// </summary>
    /// <param name="client">The client name to check.</param>
    /// <returns>True if a record with the client name exists, false otherwise.</returns>
    Task<bool> ClientExistsAsync(string client);

    /// <summary>
    /// Gets the total count of HarriTenant records.
    /// </summary>
    /// <returns>The total number of HarriTenant records.</returns>
    Task<int> GetCountAsync();
}
