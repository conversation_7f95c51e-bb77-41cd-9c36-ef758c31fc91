@page "/service-layer-test"
@using HarriTenantApp.Server.Services
@inject ServiceLayerTestService ServiceLayerTestService

<PageTitle>Service Layer Test</PageTitle>

<h1>Service Layer Test</h1>

<p>This page tests the service layer implementation including repositories, business logic, and validation.</p>

@if (loading)
{
    <div class="d-flex align-items-center">
        <strong>Running tests...</strong>
        <div class="spinner-border ms-auto" role="status" aria-hidden="true"></div>
    </div>
}
else if (testResult != null)
{
    <div class="card mb-3">
        <div class="card-header">
            <h5>
                Overall Test Result: 
                <span class="badge @(testResult.OverallSuccess ? "bg-success" : "bg-danger")">
                    @(testResult.OverallSuccess ? "PASS" : "FAIL")
                </span>
            </h5>
        </div>
        <div class="card-body">
            @if (!string.IsNullOrEmpty(testResult.GeneralError))
            {
                <div class="alert alert-danger">
                    <strong>General Error:</strong> @testResult.GeneralError
                </div>
            }

            <div class="row">
                <div class="col-md-6">
                    <h6>Test Results</h6>
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Get All Tenants
                            <span class="badge @(testResult.GetAllTenantsTest.Success ? "bg-success" : "bg-danger")">
                                @(testResult.GetAllTenantsTest.Success ? "PASS" : "FAIL")
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Get Tenant Count
                            <span class="badge @(testResult.GetTenantCountTest.Success ? "bg-success" : "bg-danger")">
                                @(testResult.GetTenantCountTest.Success ? "PASS" : "FAIL")
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Validation Tests
                            <span class="badge @(testResult.ValidationTest.Success ? "bg-success" : "bg-danger")">
                                @(testResult.ValidationTest.Success ? "PASS" : "FAIL")
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            CRUD Operations
                            <span class="badge @(testResult.CrudOperationsTest.Success ? "bg-success" : "bg-danger")">
                                @(testResult.CrudOperationsTest.Success ? "PASS" : "FAIL")
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Business Rules
                            <span class="badge @(testResult.BusinessRulesTest.Success ? "bg-success" : "bg-danger")">
                                @(testResult.BusinessRulesTest.Success ? "PASS" : "FAIL")
                            </span>
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Test Details</h6>
                    <div class="accordion" id="testDetailsAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="getAllTenantsHeader">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#getAllTenantsCollapse">
                                    Get All Tenants Test
                                </button>
                            </h2>
                            <div id="getAllTenantsCollapse" class="accordion-collapse collapse" data-bs-parent="#testDetailsAccordion">
                                <div class="accordion-body">
                                    <strong>Message:</strong> @testResult.GetAllTenantsTest.Message<br/>
                                    @if (!string.IsNullOrEmpty(testResult.GetAllTenantsTest.Details))
                                    {
                                        <strong>Details:</strong> @testResult.GetAllTenantsTest.Details
                                    }
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header" id="getTenantCountHeader">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#getTenantCountCollapse">
                                    Get Tenant Count Test
                                </button>
                            </h2>
                            <div id="getTenantCountCollapse" class="accordion-collapse collapse" data-bs-parent="#testDetailsAccordion">
                                <div class="accordion-body">
                                    <strong>Message:</strong> @testResult.GetTenantCountTest.Message<br/>
                                    @if (!string.IsNullOrEmpty(testResult.GetTenantCountTest.Details))
                                    {
                                        <strong>Details:</strong> @testResult.GetTenantCountTest.Details
                                    }
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header" id="validationHeader">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#validationCollapse">
                                    Validation Test
                                </button>
                            </h2>
                            <div id="validationCollapse" class="accordion-collapse collapse" data-bs-parent="#testDetailsAccordion">
                                <div class="accordion-body">
                                    <strong>Message:</strong> @testResult.ValidationTest.Message<br/>
                                    @if (!string.IsNullOrEmpty(testResult.ValidationTest.Details))
                                    {
                                        <strong>Details:</strong> @testResult.ValidationTest.Details
                                    }
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header" id="crudHeader">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#crudCollapse">
                                    CRUD Operations Test
                                </button>
                            </h2>
                            <div id="crudCollapse" class="accordion-collapse collapse" data-bs-parent="#testDetailsAccordion">
                                <div class="accordion-body">
                                    <strong>Message:</strong> @testResult.CrudOperationsTest.Message<br/>
                                    @if (!string.IsNullOrEmpty(testResult.CrudOperationsTest.Details))
                                    {
                                        <strong>Details:</strong> @testResult.CrudOperationsTest.Details
                                    }
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header" id="businessRulesHeader">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#businessRulesCollapse">
                                    Business Rules Test
                                </button>
                            </h2>
                            <div id="businessRulesCollapse" class="accordion-collapse collapse" data-bs-parent="#testDetailsAccordion">
                                <div class="accordion-body">
                                    <strong>Message:</strong> @testResult.BusinessRulesTest.Message<br/>
                                    @if (!string.IsNullOrEmpty(testResult.BusinessRulesTest.Details))
                                    {
                                        <strong>Details:</strong> @testResult.BusinessRulesTest.Details
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

<button class="btn btn-primary" @onclick="RunTests" disabled="@loading">
    @if (loading)
    {
        <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
        <span>Running Tests...</span>
    }
    else
    {
        <span>Run Service Layer Tests</span>
    }
</button>

@code {
    private bool loading = false;
    private ServiceLayerTestResult? testResult;

    protected override async Task OnInitializedAsync()
    {
        await RunTests();
    }

    private async Task RunTests()
    {
        loading = true;
        testResult = null;
        StateHasChanged();

        try
        {
            testResult = await ServiceLayerTestService.RunAllTestsAsync();
        }
        catch (Exception ex)
        {
            testResult = new ServiceLayerTestResult
            {
                OverallSuccess = false,
                GeneralError = ex.Message
            };
        }
        finally
        {
            loading = false;
            StateHasChanged();
        }
    }
}
