﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{827E0CD3-B72D-47B6-A68D-7590B98EB39B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HarriTenantApp.Shared", "src\HarriTenantApp.Shared\HarriTenantApp.Shared.csproj", "{A0875C81-24DD-41CB-9822-547F9BE5E800}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HarriTenantApp.Server", "src\HarriTenantApp.Server\HarriTenantApp.Server.csproj", "{D7567C1A-2CE5-4ADE-A1CD-076ED7113A68}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{0AB3BF05-4346-4AA6-1389-037BE0695223}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HarriTenantApp.Server.Tests", "tests\HarriTenantApp.Server.Tests\HarriTenantApp.Server.Tests.csproj", "{F96202A6-D134-42DB-B10D-1B3DD290FAA7}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = ".Documentation", ".Documentation", "{B8E4A3F2-1C5D-4E7A-9B8C-3F2E1D4C5A6B}"
	ProjectSection(SolutionItems) = preProject
		_Documentation\HarrTenantPRD.md = _Documentation\HarrTenantPRD.md
		_Documentation\How To Guide.md = _Documentation\How To Guide.md
		_Documentation\My Memory.md = _Documentation\My Memory.md
		_Documentation\PromptTranscript.md = _Documentation\PromptTranscript.md
		_Documentation\Support Documentation.md = _Documentation\Support Documentation.md
		_Documentation\Tasks.md = _Documentation\Tasks.md
		_Documentation\Unit Tests.md = _Documentation\Unit Tests.md
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A0875C81-24DD-41CB-9822-547F9BE5E800}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A0875C81-24DD-41CB-9822-547F9BE5E800}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A0875C81-24DD-41CB-9822-547F9BE5E800}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A0875C81-24DD-41CB-9822-547F9BE5E800}.Debug|x64.Build.0 = Debug|Any CPU
		{A0875C81-24DD-41CB-9822-547F9BE5E800}.Debug|x86.ActiveCfg = Debug|Any CPU
		{A0875C81-24DD-41CB-9822-547F9BE5E800}.Debug|x86.Build.0 = Debug|Any CPU
		{A0875C81-24DD-41CB-9822-547F9BE5E800}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A0875C81-24DD-41CB-9822-547F9BE5E800}.Release|Any CPU.Build.0 = Release|Any CPU
		{A0875C81-24DD-41CB-9822-547F9BE5E800}.Release|x64.ActiveCfg = Release|Any CPU
		{A0875C81-24DD-41CB-9822-547F9BE5E800}.Release|x64.Build.0 = Release|Any CPU
		{A0875C81-24DD-41CB-9822-547F9BE5E800}.Release|x86.ActiveCfg = Release|Any CPU
		{A0875C81-24DD-41CB-9822-547F9BE5E800}.Release|x86.Build.0 = Release|Any CPU
		{D7567C1A-2CE5-4ADE-A1CD-076ED7113A68}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D7567C1A-2CE5-4ADE-A1CD-076ED7113A68}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D7567C1A-2CE5-4ADE-A1CD-076ED7113A68}.Debug|x64.ActiveCfg = Debug|Any CPU
		{D7567C1A-2CE5-4ADE-A1CD-076ED7113A68}.Debug|x64.Build.0 = Debug|Any CPU
		{D7567C1A-2CE5-4ADE-A1CD-076ED7113A68}.Debug|x86.ActiveCfg = Debug|Any CPU
		{D7567C1A-2CE5-4ADE-A1CD-076ED7113A68}.Debug|x86.Build.0 = Debug|Any CPU
		{D7567C1A-2CE5-4ADE-A1CD-076ED7113A68}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D7567C1A-2CE5-4ADE-A1CD-076ED7113A68}.Release|Any CPU.Build.0 = Release|Any CPU
		{D7567C1A-2CE5-4ADE-A1CD-076ED7113A68}.Release|x64.ActiveCfg = Release|Any CPU
		{D7567C1A-2CE5-4ADE-A1CD-076ED7113A68}.Release|x64.Build.0 = Release|Any CPU
		{D7567C1A-2CE5-4ADE-A1CD-076ED7113A68}.Release|x86.ActiveCfg = Release|Any CPU
		{D7567C1A-2CE5-4ADE-A1CD-076ED7113A68}.Release|x86.Build.0 = Release|Any CPU
		{F96202A6-D134-42DB-B10D-1B3DD290FAA7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F96202A6-D134-42DB-B10D-1B3DD290FAA7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F96202A6-D134-42DB-B10D-1B3DD290FAA7}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F96202A6-D134-42DB-B10D-1B3DD290FAA7}.Debug|x64.Build.0 = Debug|Any CPU
		{F96202A6-D134-42DB-B10D-1B3DD290FAA7}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F96202A6-D134-42DB-B10D-1B3DD290FAA7}.Debug|x86.Build.0 = Debug|Any CPU
		{F96202A6-D134-42DB-B10D-1B3DD290FAA7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F96202A6-D134-42DB-B10D-1B3DD290FAA7}.Release|Any CPU.Build.0 = Release|Any CPU
		{F96202A6-D134-42DB-B10D-1B3DD290FAA7}.Release|x64.ActiveCfg = Release|Any CPU
		{F96202A6-D134-42DB-B10D-1B3DD290FAA7}.Release|x64.Build.0 = Release|Any CPU
		{F96202A6-D134-42DB-B10D-1B3DD290FAA7}.Release|x86.ActiveCfg = Release|Any CPU
		{F96202A6-D134-42DB-B10D-1B3DD290FAA7}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{A0875C81-24DD-41CB-9822-547F9BE5E800} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{D7567C1A-2CE5-4ADE-A1CD-076ED7113A68} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{F96202A6-D134-42DB-B10D-1B3DD290FAA7} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
	EndGlobalSection
EndGlobal
