using Microsoft.EntityFrameworkCore;
using HarriTenantApp.Server.Data;
using HarriTenantApp.Server.Models;

namespace HarriTenantApp.Server.Services;

public class DatabaseTestService
{
    private readonly ApplicationDbContext _context;

    public DatabaseTestService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            return await _context.Database.CanConnectAsync();
        }
        catch (Exception)
        {
            return false;
        }
    }

    public async Task<int> GetTenantCountAsync()
    {
        try
        {
            return await _context.HarriTenants.CountAsync();
        }
        catch (Exception)
        {
            return -1;
        }
    }

    public async Task<List<HarriTenant>> GetAllTenantsAsync()
    {
        try
        {
            return await _context.HarriTenants.ToListAsync();
        }
        catch (Exception)
        {
            return new List<HarriTenant>();
        }
    }
}
