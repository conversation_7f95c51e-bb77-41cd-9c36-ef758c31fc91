using HarriTenantApp.Server.Models;

namespace HarriTenantApp.Server.Services;

/// <summary>
/// Service for testing the service layer implementation.
/// </summary>
public class ServiceLayerTestService
{
    private readonly IHarriTenantService _tenantService;
    private readonly ILogger<ServiceLayerTestService> _logger;

    public ServiceLayerTestService(IHarriTenantService tenantService, ILogger<ServiceLayerTestService> logger)
    {
        _tenantService = tenantService ?? throw new ArgumentNullException(nameof(tenantService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Tests all service layer operations.
    /// </summary>
    public async Task<ServiceLayerTestResult> RunAllTestsAsync()
    {
        var result = new ServiceLayerTestResult();

        try
        {
            _logger.LogInformation("Starting service layer tests");

            // Test 1: Get all tenants
            result.GetAllTenantsTest = await TestGetAllTenantsAsync();

            // Test 2: Get tenant count
            result.GetTenantCountTest = await TestGetTenantCountAsync();

            // Test 3: Validation tests
            result.ValidationTest = await TestValidationAsync();

            // Test 4: CRUD operations (if we have data)
            result.CrudOperationsTest = await TestCrudOperationsAsync();

            // Test 5: Business rules
            result.BusinessRulesTest = await TestBusinessRulesAsync();

            result.OverallSuccess = result.GetAllTenantsTest.Success &&
                                   result.GetTenantCountTest.Success &&
                                   result.ValidationTest.Success &&
                                   result.CrudOperationsTest.Success &&
                                   result.BusinessRulesTest.Success;

            _logger.LogInformation("Service layer tests completed. Overall success: {Success}", result.OverallSuccess);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running service layer tests");
            result.OverallSuccess = false;
            result.GeneralError = ex.Message;
        }

        return result;
    }

    private async Task<TestResult> TestGetAllTenantsAsync()
    {
        try
        {
            var result = await _tenantService.GetAllTenantsAsync();
            return new TestResult
            {
                Success = result.IsSuccess,
                Message = result.IsSuccess ? $"Retrieved {result.Data?.Count() ?? 0} tenants" : result.ErrorMessage ?? "Unknown error",
                Details = result.IsSuccess ? $"Data type: {result.Data?.GetType().Name}" : null
            };
        }
        catch (Exception ex)
        {
            return new TestResult { Success = false, Message = ex.Message };
        }
    }

    private async Task<TestResult> TestGetTenantCountAsync()
    {
        try
        {
            var result = await _tenantService.GetTenantCountAsync();
            return new TestResult
            {
                Success = result.IsSuccess,
                Message = result.IsSuccess ? $"Tenant count: {result.Data}" : result.ErrorMessage ?? "Unknown error"
            };
        }
        catch (Exception ex)
        {
            return new TestResult { Success = false, Message = ex.Message };
        }
    }

    private async Task<TestResult> TestValidationAsync()
    {
        try
        {
            // Test with invalid tenant
            var invalidTenant = new HarriTenant
            {
                Client = "", // Invalid - empty
                Secret = "123", // Invalid - too short
                TokenUrl = "invalid-url", // Invalid URL
                BaseUrl = "also-invalid"
            };

            var validationResult = await _tenantService.ValidateTenantAsync(invalidTenant);
            
            if (validationResult.IsSuccess)
            {
                return new TestResult { Success = false, Message = "Validation should have failed for invalid tenant" };
            }

            return new TestResult
            {
                Success = true,
                Message = $"Validation correctly failed with {validationResult.ValidationErrors.Count} errors",
                Details = string.Join("; ", validationResult.ValidationErrors)
            };
        }
        catch (Exception ex)
        {
            return new TestResult { Success = false, Message = ex.Message };
        }
    }

    private async Task<TestResult> TestCrudOperationsAsync()
    {
        try
        {
            // Test getting a non-existent tenant
            var getResult = await _tenantService.GetTenantByIdAsync(Guid.NewGuid());
            
            if (getResult.IsSuccess)
            {
                return new TestResult { Success = false, Message = "Should not have found non-existent tenant" };
            }

            // Test with empty GUID
            var emptyGuidResult = await _tenantService.GetTenantByIdAsync(Guid.Empty);
            
            if (emptyGuidResult.IsSuccess)
            {
                return new TestResult { Success = false, Message = "Should not accept empty GUID" };
            }

            return new TestResult
            {
                Success = true,
                Message = "CRUD operations validation working correctly",
                Details = "Non-existent tenant handling and empty GUID validation working"
            };
        }
        catch (Exception ex)
        {
            return new TestResult { Success = false, Message = ex.Message };
        }
    }

    private async Task<TestResult> TestBusinessRulesAsync()
    {
        try
        {
            // Test URL validation
            var tenantWithBadUrls = new HarriTenant
            {
                Client = "Test Client",
                Secret = "ValidSecret123",
                TokenUrl = "not-a-url",
                BaseUrl = "also-not-a-url",
                IsCorporateTenant = false
            };

            var urlValidationResult = await _tenantService.ValidateTenantAsync(tenantWithBadUrls);
            
            if (urlValidationResult.IsSuccess)
            {
                return new TestResult { Success = false, Message = "URL validation should have failed" };
            }

            // Test client name rules
            var tenantWithBadClient = new HarriTenant
            {
                Client = "A", // Too short
                Secret = "ValidSecret123",
                TokenUrl = "https://valid.url",
                BaseUrl = "https://valid.url",
                IsCorporateTenant = false
            };

            var clientValidationResult = await _tenantService.ValidateTenantAsync(tenantWithBadClient);
            
            if (clientValidationResult.IsSuccess)
            {
                return new TestResult { Success = false, Message = "Client name validation should have failed" };
            }

            return new TestResult
            {
                Success = true,
                Message = "Business rules validation working correctly",
                Details = "URL validation and client name rules enforced"
            };
        }
        catch (Exception ex)
        {
            return new TestResult { Success = false, Message = ex.Message };
        }
    }
}

/// <summary>
/// Result of service layer tests.
/// </summary>
public class ServiceLayerTestResult
{
    public bool OverallSuccess { get; set; }
    public string? GeneralError { get; set; }
    public TestResult GetAllTenantsTest { get; set; } = new();
    public TestResult GetTenantCountTest { get; set; } = new();
    public TestResult ValidationTest { get; set; } = new();
    public TestResult CrudOperationsTest { get; set; } = new();
    public TestResult BusinessRulesTest { get; set; } = new();
}

/// <summary>
/// Result of an individual test.
/// </summary>
public class TestResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? Details { get; set; }
}
