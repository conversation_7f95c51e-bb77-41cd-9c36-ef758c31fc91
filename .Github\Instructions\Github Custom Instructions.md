---
# Update: Clarification on Required Documentation Files (2025-06-04)

## What Changed
- Clarified that `Support Documentation.md` is a required file in every project, alongside `My Memory.md` and `PromptTranscript.md`.
- Updated the introductory text and the "Required Documentation Files" section to explicitly state that all three files are mandatory and must be created at project start.
- Added a note in the checklist to verify the existence of all three required files before any coding begins.

## Why
- To prevent confusion and ensure that `Support Documentation.md` is always created and maintained as part of the core documentation set.
- To align the instructions with the actual workflow and requirements enforced in this project.

## How to Use
- Always create `My Memory.md`, `Support Documentation.md`, and `PromptTranscript.md` in the `_Documentation` folder at the start of every project.
- Ensure all three files have proper frontmatter and are kept up to date as described in the instructions.

---

# AI Assistant Coding Instructions

## Standards and Guidelines

- **Coding Standards**: Follow all guidelines specified in `Github Coding Standards Instructions.md`
- **Unit Testing**: Implement all practices outlined in `Github Unit Test Guidelines Instructions.md`

## Documentation Management

### Required Documentation Structure

**CRITICAL**: For every solution, you MUST create and maintain a `_Documentation\` directory in the file system with a corresponding `.Documentation` folder in the solution structure.

**Directory Setup Requirements:**
1. Create `_Documentation\` directory at the solution root (file system)
2. Create `.Documentation` folder in the solution structure (project/solution)
3. Ensure ALL files in `_Documentation\` are also included in the `.Documentation` solution folder
4. Use subfolders to organize documentation types (specs, guides, logs, etc.)

**Frontmatter Requirement:**
- **ALL** `.md` files in the `_Documentation` folder MUST include frontmatter explaining the file's purpose
- Frontmatter serves as AI guidance for what content belongs in each file
- Include clear documentation objectives and content guidelines in the frontmatter

### Required Documentation Files

You MUST maintain these three core files in every project:

#### 1. `My Memory.md` - AI Knowledge Base (REQUIRED)

**FRONTMATTER EXAMPLE:**
```yaml
---
purpose: "AI Knowledge Base for Project Context and Patterns - Prevents Multiple Codebase Analysis"
audience: "AI Assistants (Primary), Human Developers (Secondary)"
instructions: |
  This file serves as persistent memory for AI assistants working on this project.
  Document architectural decisions, coding patterns, dependencies, known issues,
  and lessons learned to prevent redundant codebase analysis and multiple explorations
  of the same code sections. Always consult this file FIRST before examining code.
update_triggers: "After major tasks, architectural changes, or discovering new insights"
---
```

**PURPOSE**: Your persistent memory for this project to avoid redundant codebase analysis.

**MANDATORY WORKFLOW**:
- **ALWAYS consult this file FIRST** before examining any code
- Use this as your primary reference for project understanding
- **IMMEDIATELY document** any new patterns, issues, or insights you discover

**REQUIRED CONTENT**:
- Architectural decisions and rationale
- Established coding patterns and conventions  
- Critical dependencies and their purposes
- Known issues, edge cases, and workarounds
- Lessons learned and best practices
- Project structure overview
- Key components and their relationships

**UPDATE TRIGGERS**:
- After completing any major task
- When discovering new insights during code exploration
- After resolving complex issues
- When architectural changes are made
- Before concluding work sessions (unless no meaningful updates exist)

#### 2. `Support Documentation.md` - Human Developer Guide (REQUIRED)

**FRONTMATTER EXAMPLE:**
```yaml
---
purpose: "Comprehensive project documentation for human developers and support teams"
audience: "Human Developers, Support Teams, Stakeholders"
instructions: |
  Document setup procedures, deployment instructions, troubleshooting guides,
  API documentation, and user workflows. Focus on practical, actionable information
  for team members who need to understand, maintain, or support this project.
update_triggers: "When user-facing features change, deployment procedures update, or new setup requirements are added"
---
```

**PURPOSE**: Comprehensive reference for human developers and support teams.

**REQUIRED CONTENT**:
- Setup and installation instructions
- Deployment procedures
- Troubleshooting guides
- API documentation
- Configuration details
- User guides and workflows

**AUDIENCE**: Future developers, support teams, stakeholders

#### 3. `PromptTranscript.md` - Development History (REQUIRED)

**FRONTMATTER EXAMPLE:**
```yaml
---
title: "Prompt Transcript"
description: "Verbatim log of all prompts and responses during project development"
author: "AI Assistant"
created: "[YYYY-MM-DD]"
updated: "[YYYY-MM-DD]"
version: "1.0"
purpose: "Verbatim log of meaningful user prompts and AI questions for development context"
audience: "AI Assistants, Project Stakeholders"
instructions: |
  Log ALL substantial user prompts and meaningful AI clarification questions verbatim.
  Maintain chronological order with session headers and date stamps. Record the COMPLETE
  conversation flow including multi-turn exchanges. Do not summarize or condense.
  This serves as development history and context for future AI interactions.
update_triggers: "Immediately upon receiving any meaningful user prompt or asking substantial clarification questions"
---
```

**PURPOSE**: Verbatim log of all meaningful user prompts and AI questions for development context.

**CRITICAL WORKFLOW**: **IMMEDIATELY** upon receiving any user prompt, update `PromptTranscript.md` FIRST before performing any other activities, analysis, or responses.

**ENHANCED LOGGING RULES**:
- **DO LOG VERBATIM**: 
  - ALL explicit user prompts containing substantial intent, requests, or instructions
  - AI clarification questions to the user that seek requirements or project direction
  - Complete conversation exchanges that span multiple turns
  - Context-setting prompts and responses that establish project direction
  - Technical decisions made during conversations
  - File operations, git commits, and major actions taken
  - Error resolutions and troubleshooting exchanges
- **DO NOT LOG**: 
  - System-generated prompts or boilerplate
  - Configuration/instruction file content (unless specifically discussed)
  - Trivial user responses ("Continue", "yes", "ok") unless they conclude important decisions
  - Simple AI confirmations without substantial content
  - Repetitive status updates without new information

**ENHANCED FORMAT REQUIREMENTS**:
- Include complete frontmatter with title, description, author, dates, and version
- Organize by session numbers and date headers
- Record COMPLETE prompts and responses, not summaries
- Use clear section headers for different conversation topics
- Include context about file operations, commits, and major actions
- Maintain chronological order within sessions
- Clearly mark user prompts vs AI responses vs system actions
- Log the FULL context of multi-turn conversations, not just individual exchanges

**CRITICAL IMPROVEMENT**: Unlike initial project handling, NEVER summarize or condense conversations. The transcript must capture the COMPLETE development narrative including decision rationale, alternative approaches considered, and full context of requirements gathering.

### Additional Documentation (Create as Needed)

**ALL additional `.md` files MUST include frontmatter following this pattern:**

```yaml
---
purpose: "[Brief description of what this file documents]"
audience: "[Who will use this documentation]"
instructions: |
  [Specific guidance on what content belongs in this file and how to maintain it]
update_triggers: "[When this file should be updated]"
---
```

**Common Additional Files:**
- **Diagrams**: Mermaid files (`.mmd`) for architecture and flow charts
- **API Documentation**: Dedicated endpoint specification files
- **Database Schema**: Data model and relationship documentation  
- **Configuration Guides**: Environment and setup details

## Mandatory Documentation Update Protocol

### Update Requirements

You MUST update documentation:
- After completing each major task or feature
- When making architectural decisions
- After bug fixes that reveal system insights
- When implementing new patterns or approaches

### Quality Standards Checklist

Before completing any task, verify:
- [ ] Code follows established standards
- [ ] Unit tests meet guidelines with adequate coverage
- [ ] `My Memory.md` updated with new insights or changes
- [ ] `Support Documentation.md` reflects user-facing changes
- [ ] Diagrams updated if system flow changed
- [ ] All documentation is accurate and current
- [ ] Documentation files exist in both `_Documentation\` and `.Documentation`

## File Organization Structure

```
_Documentation/
├── My Memory.md                    (REQUIRED)
├── Support Documentation.md        (REQUIRED)
├── PromptTranscript.md            (REQUIRED)
├── diagrams/
│   ├── architecture.mmd
│   └── data-flow.mmd
└── api/
    └── endpoints.md
```

## AI Assistant Best Practices

1. **Prompt Logging First**: **IMMEDIATELY** update `PromptTranscript.md` upon receiving any user prompt, before any other activity. Record the COMPLETE prompt verbatim, not a summary.
2. **Memory First**: Always check `My Memory.md` before code analysis (after prompt logging)
3. **Read Frontmatter**: Always read the frontmatter of documentation files to understand their purpose and content guidelines
4. **Document Discoveries**: Immediately record new insights to prevent re-analysis
5. **Maintain Context**: Keep documentation current to support future interactions
6. **Clear Communication**: Write documentation for both AI and human consumption
7. **Consistent Updates**: Update documentation as part of every significant task completion
8. **Complete Transcript**: Never summarize conversations in PromptTranscript.md - capture the full development narrative including decision-making process, alternatives considered, and complete requirement gathering exchanges

## Implementation Checklist

**Before starting any coding task:**
- [ ] `My Memory.md`, `Support Documentation.md`, and `PromptTranscript.md` exist in `_Documentation` and are initialized with frontmatter
- [ ] `PromptTranscript.md` updated FIRST with the COMPLETE prompt verbatim (no summaries)
- [ ] `My Memory.md` consulted for existing context
- [ ] Frontmatter of relevant documentation files reviewed for content guidance
- [ ] Previous documentation reviewed before proceeding
- [ ] Multi-turn conversation context maintained in transcript

**Before completing any coding task:**
- [ ] All required documentation files are updated
- [ ] New insights are captured in `My Memory.md`
- [ ] Documentation reflects current system state
- [ ] All `.md` files have proper frontmatter with purpose and instructions
- [ ] Quality standards checklist completed
- [ ] `PromptTranscript.md` updated with any significant responses, decisions, or actions taken