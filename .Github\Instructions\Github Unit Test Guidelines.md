# Enhanced Coding LLM Prompt Guidelines for Unit Testing

## Core Testing Framework Requirements

Always use these testing tools:
- xUnit for test framework
- Moq for mocking
- AutoFixture for test data generation
- AutoMoq for automatic mock creation
- FluentAssertions for readable assertions

## AutoFixture Best Practices

### Rule 1: Parameter-Based Variable Definition
Define variables in the parameter list whenever possible to reduce arrangement code and leverage AutoFixture's automatic creation capabilities.

### Rule 2: Minimize Interface Declarations
Do not explicitly define interfaces unless they are referenced elsewhere in the unit test. Let AutoFixture automatically create and inject mocked dependencies.

### Rule 3: Direct Interface Variable Definition
When defining interface variables, declare them directly as the interface type rather than as mocks:
- Use `IEmployee employee` instead of `Mock<IEmployee> employeeMock`
- AutoMoq will handle the conversion to mocks automatically

### Rule 4: Leverage Attribute-Based Freezing
Use `[Frozen]` attribute to define variables that need consistent values throughout the test. Once a variable is frozen:
- It will be consistently injected wherever needed
- You don't need to manually set it up in arrangement code
- All dependencies requiring that type will receive the same instance

### Rule 5: Use Parameterized Testing
Where appropriate, use parameterized test methods with the `[InlineAutoMoqData]` attribute to test multiple scenarios within a single test method definition.

### Rule 6: Document Test Scenarios
When testing multiple scenarios (per Rule 5), add a descriptive parameter to explain each scenario for improved readability and documentation.

## Implementation Notes

- You don't need to explicitly code attributes for these rules unless there's something unusual
- Basic attributes have been predefined in the testing framework
- Use `Mock.Get()` when working with interface instances that were automatically created by AutoFixture
- Minimize arrangement code by leveraging parameter injection

## Example Transformation Pattern

**From:**
```csharp
// Manually creating mocks
var mockService = new Mock<IService>();
var sut = new SystemUnderTest(mockService.Object);

// Manually creating test data
var testData = new TestData { Property = "value" };

// Manual setup
mockService.Setup(x => x.Method()).Returns(result);
```

**To**
```csharp
[Theory]
[AutoMoqData]
public void Test_Scenario_ExpectedOutcome(
    [Frozen] IService service,
    TestData testData,
    SystemUnderTest sut)
{
    // Minimal arrangement - only what's specific to this test
    Mock.Get(service).Setup(x => x.Method()).Returns(result);
    
    // Act & Assert
}
```

## Additional Guidelines

1. Focus on testing behavior, not implementation details
2. Use descriptive test names that explain the scenario and expected outcome
3. Structure tests in Arrange-Act-Assert pattern, even when using AutoFixture
4. For complex test data setup, consider creating custom AutoFixture customizations
5. Use `It.IsAny<T>()` for parameters that aren't relevant to the test
6. Verify only the interactions that matter for the specific test case