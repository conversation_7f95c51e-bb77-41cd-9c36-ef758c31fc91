@page "/database-test"
@using HarriTenantApp.Server.Services
@using HarriTenantApp.Server.Models
@inject DatabaseTestService DatabaseTestService

<PageTitle>Database Test</PageTitle>

<h1>Database Connection Test</h1>

<p>This page tests the database connection and displays HarriTenant data.</p>

@if (loading)
{
    <p><em>Loading...</em></p>
}
else
{
    <div class="card mb-3">
        <div class="card-header">
            <h5>Connection Status</h5>
        </div>
        <div class="card-body">
            <p>
                <strong>Can Connect:</strong> 
                <span class="badge @(canConnect ? "bg-success" : "bg-danger")">
                    @(canConnect ? "Yes" : "No")
                </span>
            </p>
            <p><strong>Tenant Count:</strong> @tenantCount</p>
        </div>
    </div>

    @if (tenants.Any())
    {
        <div class="card">
            <div class="card-header">
                <h5>HarriTenant Records</h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Tenant ID</th>
                            <th>Client</th>
                            <th>Name</th>
                            <th>Is Corporate</th>
                            <th>Is Active</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var tenant in tenants)
                        {
                            <tr>
                                <td>@tenant.TenantId</td>
                                <td>@tenant.Client</td>
                                <td>@(tenant.Name ?? "N/A")</td>
                                <td>@tenant.IsCorporateTenant</td>
                                <td>@(tenant.IsActive?.ToString() ?? "N/A")</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    }
    else if (canConnect)
    {
        <div class="alert alert-info">
            <strong>No HarriTenant records found.</strong> The database connection is working, but the table is empty.
        </div>
    }
}

<button class="btn btn-primary" @onclick="RefreshData">Refresh Data</button>

@code {
    private bool loading = true;
    private bool canConnect = false;
    private int tenantCount = 0;
    private List<HarriTenant> tenants = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        loading = true;
        try
        {
            canConnect = await DatabaseTestService.TestConnectionAsync();
            tenantCount = await DatabaseTestService.GetTenantCountAsync();
            tenants = await DatabaseTestService.GetAllTenantsAsync();
        }
        catch (Exception ex)
        {
            // Log exception if needed
            canConnect = false;
            tenantCount = -1;
            tenants = new List<HarriTenant>();
        }
        finally
        {
            loading = false;
        }
    }

    private async Task RefreshData()
    {
        await LoadData();
    }
}
