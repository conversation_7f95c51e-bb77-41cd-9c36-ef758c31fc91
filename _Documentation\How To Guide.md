---
purpose: "How-To Guide for Common Tasks and Workflows"
audience: "Developers, AI Assistants"
instructions: |
  Document step-by-step instructions for common development, deployment, and troubleshooting tasks.
  This file should make it easy for anyone to follow best practices and repeat key workflows.
---

# How To Guide

## Development Setup

### Prerequisites
- .NET 8.0 SDK
- SQL Server (local instance)
- Visual Studio 2022 or VS Code

### Initial Setup
1. Clone the repository
2. Open `HarriTenantApp.sln` in Visual Studio
3. Restore NuGet packages: `dotnet restore`
4. Verify database connection string in `src/HarriTenantApp.Server/appsettings.json`

### Running the Application
1. Set `HarriTenantApp.Server` as startup project
2. Press F5 or run `dotnet run` from `src/HarriTenantApp.Server` directory
3. Navigate to `http://localhost:5097` or `https://localhost:7125`
4. Test database connection at `http://localhost:5097/database-test`

### Database Connection
- **Working Connection String:** `data source=.;initial catalog=dbNServiceBus_Employment_Config;integrated security=true;enlist=false;TrustServerCertificate=true;`
- Database: `dbNServiceBus_Employment_Config`
- Table: `HarriTenant`

### Building and Testing
1. Build solution: `dotnet build`
2. Run tests: `dotnet test`
3. Check for compilation errors in Output window

### Troubleshooting

#### Database Connection Issues
- Ensure SQL Server is running
- Verify connection string format
- Check if `TrustServerCertificate=true` is included for SSL issues
- Test connection using the `/database-test` page

#### Build Issues
- Clean and rebuild: `dotnet clean && dotnet build`
- Check NuGet package references
- Verify .NET 8.0 SDK is installed
