using Microsoft.EntityFrameworkCore;
using HarriTenantApp.Server.Data;
using HarriTenantApp.Server.Models;

namespace HarriTenantApp.Server.Repositories;

/// <summary>
/// Repository implementation for HarriTenant data access operations.
/// </summary>
public class HarriTenantRepository : IHarriTenantRepository
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<HarriTenantRepository> _logger;

    public HarriTenantRepository(ApplicationDbContext context, ILogger<HarriTenantRepository> logger)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <inheritdoc />
    public async Task<IEnumerable<HarriTenant>> GetAllAsync()
    {
        try
        {
            _logger.LogDebug("Retrieving all HarriTenant records");
            return await _context.HarriTenants
                .OrderBy(t => t.Client)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all HarriTenant records");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<HarriTenant?> GetByIdAsync(Guid tenantId)
    {
        try
        {
            _logger.LogDebug("Retrieving HarriTenant record with ID: {TenantId}", tenantId);
            return await _context.HarriTenants
                .FirstOrDefaultAsync(t => t.TenantId == tenantId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving HarriTenant record with ID: {TenantId}", tenantId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<HarriTenant>> GetByClientAsync(string client)
    {
        try
        {
            _logger.LogDebug("Retrieving HarriTenant records for client: {Client}", client);
            return await _context.HarriTenants
                .Where(t => t.Client.Contains(client))
                .OrderBy(t => t.Client)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving HarriTenant records for client: {Client}", client);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<HarriTenant>> GetActiveTenantsAsync()
    {
        try
        {
            _logger.LogDebug("Retrieving active HarriTenant records");
            return await _context.HarriTenants
                .Where(t => t.IsActive == true)
                .OrderBy(t => t.Client)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving active HarriTenant records");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<HarriTenant> CreateAsync(HarriTenant tenant)
    {
        try
        {
            _logger.LogDebug("Creating new HarriTenant record for client: {Client}", tenant.Client);
            
            // Ensure TenantId is generated if not provided
            if (tenant.TenantId == Guid.Empty)
            {
                tenant.TenantId = Guid.NewGuid();
            }

            _context.HarriTenants.Add(tenant);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Successfully created HarriTenant record with ID: {TenantId}", tenant.TenantId);
            return tenant;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating HarriTenant record for client: {Client}", tenant.Client);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<HarriTenant> UpdateAsync(HarriTenant tenant)
    {
        try
        {
            _logger.LogDebug("Updating HarriTenant record with ID: {TenantId}", tenant.TenantId);

            _context.Entry(tenant).State = EntityState.Modified;
            await _context.SaveChangesAsync();

            _logger.LogInformation("Successfully updated HarriTenant record with ID: {TenantId}", tenant.TenantId);
            return tenant;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating HarriTenant record with ID: {TenantId}", tenant.TenantId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<bool> DeleteAsync(Guid tenantId)
    {
        try
        {
            _logger.LogDebug("Deleting HarriTenant record with ID: {TenantId}", tenantId);

            var tenant = await _context.HarriTenants.FindAsync(tenantId);
            if (tenant == null)
            {
                _logger.LogWarning("HarriTenant record with ID: {TenantId} not found for deletion", tenantId);
                return false;
            }

            _context.HarriTenants.Remove(tenant);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Successfully deleted HarriTenant record with ID: {TenantId}", tenantId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting HarriTenant record with ID: {TenantId}", tenantId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<bool> ExistsAsync(Guid tenantId)
    {
        try
        {
            return await _context.HarriTenants.AnyAsync(t => t.TenantId == tenantId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking existence of HarriTenant record with ID: {TenantId}", tenantId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<bool> ClientExistsAsync(string client)
    {
        try
        {
            return await _context.HarriTenants.AnyAsync(t => t.Client == client);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking existence of HarriTenant record with client: {Client}", client);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<int> GetCountAsync()
    {
        try
        {
            return await _context.HarriTenants.CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting count of HarriTenant records");
            throw;
        }
    }
}
