@using HarriTenantApp.Server.Services
@inject ToastService ToastService
@implements IDisposable

<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
    @foreach (var toast in toasts)
    {
        <div class="toast show @GetToastClass(toast.Type)" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="@GetToastIcon(toast.Type) me-2"></i>
                <strong class="me-auto">@(toast.Title ?? GetDefaultTitle(toast.Type))</strong>
                <small class="text-muted">@GetTimeAgo(toast.CreatedAt)</small>
                <button type="button" class="btn-close" @onclick="() => RemoveToast(toast.Id)" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                @toast.Message
            </div>
        </div>
    }
</div>

@code {
    private List<ToastMessage> toasts = new();

    protected override void OnInitialized()
    {
        ToastService.OnToastAdded += AddToast;
        ToastService.OnToastRemoved += RemoveToastById;
        toasts = ToastService.GetToasts().ToList();
    }

    private void AddToast(ToastMessage toast)
    {
        toasts.Add(toast);
        InvokeAsync(StateHasChanged);
    }

    private void RemoveToastById(string id)
    {
        var toast = toasts.FirstOrDefault(t => t.Id == id);
        if (toast != null)
        {
            toasts.Remove(toast);
            InvokeAsync(StateHasChanged);
        }
    }

    private void RemoveToast(string id)
    {
        ToastService.RemoveToast(id);
    }

    private string GetToastClass(ToastType type) => type switch
    {
        ToastType.Success => "border-success",
        ToastType.Error => "border-danger",
        ToastType.Warning => "border-warning",
        ToastType.Info => "border-info",
        _ => "border-secondary"
    };

    private string GetToastIcon(ToastType type) => type switch
    {
        ToastType.Success => "oi oi-circle-check text-success",
        ToastType.Error => "oi oi-warning text-danger",
        ToastType.Warning => "oi oi-warning text-warning",
        ToastType.Info => "oi oi-info text-info",
        _ => "oi oi-info text-secondary"
    };

    private string GetDefaultTitle(ToastType type) => type switch
    {
        ToastType.Success => "Success",
        ToastType.Error => "Error",
        ToastType.Warning => "Warning",
        ToastType.Info => "Information",
        _ => "Notification"
    };

    private string GetTimeAgo(DateTime createdAt)
    {
        var timeSpan = DateTime.Now - createdAt;
        if (timeSpan.TotalMinutes < 1)
            return "just now";
        if (timeSpan.TotalMinutes < 60)
            return $"{(int)timeSpan.TotalMinutes}m ago";
        if (timeSpan.TotalHours < 24)
            return $"{(int)timeSpan.TotalHours}h ago";
        return createdAt.ToString("MMM dd");
    }

    public void Dispose()
    {
        ToastService.OnToastAdded -= AddToast;
        ToastService.OnToastRemoved -= RemoveToastById;
    }
}
