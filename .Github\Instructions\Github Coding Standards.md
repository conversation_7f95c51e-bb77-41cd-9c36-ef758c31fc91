# Coding Standards

## 1. Tech Stack

### Core Technologies
- **Language**: C# targeting .NET 8.0 (preferred) or .NET Framework (legacy projects)
- **Frontend**: Blazor Server for web applications
- **Backend**: ASP.NET Core for .NET 8.0 projects, ASP.NET for .NET Framework projects
- **Database**: SQL Server
- **ORM**: Entity Framework Core (preferred for .NET 8.0), Entity Framework (for .NET Framework)

### Key Libraries and Frameworks
- **Dependency Injection**: Microsoft.Extensions.DependencyInjection (built-in ASP.NET Core DI)
- **Logging**: NLog
- **JSON Serialization**: Newtonsoft.Json
- **HTTP Client**: RestSharp for external API calls
- **Testing Framework**: xUnit (see 'Github Unit Test Guidelines.md' for details)

### Package Management
- **Primary Source**: NuGet
- **Custom Packages**: Azure DevOps package feeds
- **Configuration**: 
  - .NET Core: appsettings.json
  - .NET Framework: App.Config

### REST APIs
- Framework selection depends on project requirements
- Follow RESTful principles and conventions
- Use appropriate HTTP status codes and methods

## 2. File and Namespace Organization
- Use `using` directives outside the namespace, grouped and sorted alphabetically.
- Place system namespaces before third-party and project namespaces.
- Use a single namespace per file, matching the folder structure.

## 3. Framework and Project Configuration
- Target .NET 8.0 (`<TargetFramework>net8.0</TargetFramework>`) for all C# projects.
- Ensure consistent framework targeting across all projects in the solution.
- Use nullable reference types enabled (`<Nullable>enable</Nullable>`).

## 4. Class and Interface Design
- Use `public` for types intended for external use; otherwise, use `internal`.
- Interface names must start with `I` (e.g., `IJobCodeRepository`).
- Class names should be PascalCase and descriptive (e.g., `JobCodeRepository`).
- Prefer partial classes only for large or auto-generated code.

## 5. Method and Property Naming
- Use PascalCase for method, property, and event names.
- Use camelCase for local variables and method parameters.
- Boolean properties and methods should be named as predicates (e.g., `IsActive`, `HasPermission`).

## 6. Method Structure and Formatting
- Use expression-bodied members for simple properties and methods when appropriate.
- Use explicit access modifiers for all members.
- Place method parameters on separate lines if the list is long.
- Use single blank lines to separate methods.

## 7. Bracing and Indentation
- Use Allman style braces (each brace on its own line).
- Indent with 4 spaces; do not use tabs.
- Always use braces for `if`, `else`, `for`, `foreach`, `while`, and `do` blocks, even if the block contains a single statement.

## 8. Null and Exception Handling
- Use `string.IsNullOrEmpty` or `string.IsNullOrWhiteSpace` for string checks.
- Use try-catch blocks for exception-prone code; log exceptions with context.
- Avoid catching general `Exception` unless necessary; catch specific exceptions when possible.

## 9. Async and Await
- Use `async` and `await` for asynchronous operations.
- Name async methods with the `Async` suffix (e.g., `HireEmployeeAsync`).
- Avoid using `async void` except for event handlers.

## 10. Comments and Documentation
- Use XML documentation comments (`///`) for public APIs.
- Use inline comments sparingly and only to clarify complex logic.
- Remove commented-out code unless it is temporary and marked with a `TODO`.

## 11. Constants and Magic Values
- Use `const` or `static readonly` for constant values.
- Do not use magic numbers or strings; define them as named constants.  Magic numbers / string are hard-coded values.
- Remove commented-out code unless it is temporary and marked with a `TODO`.and Await
- Use `async` and `await` for asynchronous operations.
- Name async methods with the `Async` suffix (e.g., `HireEmployeeAsync`).
- Avoid using `async void` except for event handlers.nd Exception Handling
- Use `string.IsNullOrEmpty` or `string.IsNullOrWhiteSpace` for string checks.
- Use try-catch blocks for exception-prone code; log exceptions with context.
- Avoid catching general `Exception` unless necessary; catch specific exceptions when possible.g and Indentation
- Use Allman style braces (each brace on its own line).
- Indent with 4 spaces; do not use tabs.
- Always use braces for `if`, `else`, `for`, `foreach`, `while`, and `do` blocks, even if the block contains a single statement.tructure and Formatting
- Use expression-bodied members for simple properties and methods when appropriate.
- Use explicit access modifiers for all members.
- Place method parameters on separate lines if the list is long.
- Use single blank lines to separate methods.and Property Naming
- Use PascalCase for method, property, and event names.
- Use camelCase for local variables and method parameters.
- Boolean properties and methods should be named as predicates (e.g., `IsActive`, `HasPermission`).d Interface Design
- Use `public` for types intended for external use; otherwise, use `internal`.
- Interface names must start with `I` (e.g., `IJobCodeRepository`).
- Class names should be PascalCase and descriptive (e.g., `JobCodeRepository`).
- Prefer partial classes only for large or auto-generated code.rk and Project Configuration
- Target .NET 8.0 (`<TargetFramework>net8.0</TargetFramework>`) for all C# projects.
- Ensure consistent framework targeting across all projects in the solution.
- Use nullable reference types enabled (`<Nullable>enable</Nullable>`).

### Core Technologies
- **Language**: C# targeting .NET 8.0 (preferred) or .NET Framework (legacy projects)
- **Frontend**: Blazor Server for web applications
- **Backend**: ASP.NET Core for .NET 8.0 projects, ASP.NET for .NET Framework projects
- **Database**: SQL Server
- **ORM**: Entity Framework Core (preferred for .NET 8.0), Entity Framework (for .NET Framework)

### Key Libraries and Frameworks
- **Dependency Injection**: Microsoft.Extensions.DependencyInjection (built-in ASP.NET Core DI)
- **Logging**: NLog
- **JSON Serialization**: Newtonsoft.Json
- **HTTP Client**: RestSharp for external API calls
- **Testing Framework**: xUnit (see 'Github Unit Test Guidelines.md' for details)

### Package Management
- **Primary Source**: NuGet
- **Custom Packages**: Azure DevOps package feeds
- **Configuration**: 
  - .NET Core: appsettings.json
  - .NET Framework: App.Config

### REST APIs
- Framework selection depends on project requirements
- Follow RESTful principles and conventions
- Use appropriate HTTP status codes and methods

## 2. File and Namespace Organization
- Use `using` directives outside the namespace, grouped and sorted alphabetically.
- Place system namespaces before third-party and project namespaces.
- Use a single namespace per file, matching the folder structure.

## 3. Framework and Project Configuration
- Target .NET 8.0 (`<TargetFramework>net8.0</TargetFramework>`) for all C# projects.
- Ensure consistent framework targeting across all projects in the solution.
- Use nullable reference types enabled (`<Nullable>enable</Nullable>`).

## 4. Class and Interface Design
- Use `public` for types intended for external use; otherwise, use `internal`.
- Interface names must start with `I` (e.g., `IJobCodeRepository`).
- Class names should be PascalCase and descriptive (e.g., `JobCodeRepository`).
- Prefer partial classes only for large or auto-generated code.

## 5. Method and Property Naming
- Use PascalCase for method, property, and event names.
- Use camelCase for local variables and method parameters.
- Boolean properties and methods should be named as predicates (e.g., `IsActive`, `HasPermission`).

## 6. Method Structure and Formatting
- Use expression-bodied members for simple properties and methods when appropriate.
- Use explicit access modifiers for all members.
- Place method parameters on separate lines if the list is long.
- Use single blank lines to separate methods.

## 7. Bracing and Indentation
- Use Allman style braces (each brace on its own line).
- Indent with 4 spaces; do not use tabs.
- Always use braces for `if`, `else`, `for`, `foreach`, `while`, and `do` blocks, even if the block contains a single statement.

## 8. Null and Exception Handling
- Use `string.IsNullOrEmpty` or `string.IsNullOrWhiteSpace` for string checks.
- Use try-catch blocks for exception-prone code; log exceptions with context.
- Avoid catching general `Exception` unless necessary; catch specific exceptions when possible.

## 9. Async and Await
- Use `async` and `await` for asynchronous operations.
- Name async methods with the `Async` suffix (e.g., `HireEmployeeAsync`).
- Avoid using `async void` except for event handlers.

## 10. Comments and Documentation
- Use XML documentation comments (`///`) for public APIs.
- Use inline comments sparingly and only to clarify complex logic.
- Remove commented-out code unless it is temporary and marked with a `TODO`.

## 11. Constants and Magic Values
- Use `const` or `static readonly` for constant values.
- Do not use magic numbers or strings; define them as named constants.  Magic numbers / string are hard-coded values.

## 12. Collections and LINQ
- Prefer `var` when the type is obvious from the right side of the assignment.
- Use explicit types when the type is not clear.
- Use LINQ for collection queries; avoid complex nested queries.

## 13. Test Code
- Use `[Fact]` or `[Theory]` for xUnit tests.
- Test method names should describe the scenario and expected outcome.  Explicit outcome must be understandable.  Values such as 'ShouldRunAsExpected' are unacceptable.
- Use Arrange-Act-Assert pattern in tests.
- For validation and mocking details, see 'Github Unit Test Guidelines.md'.

## 14. General Practices
- Avoid regions except for large files with many members.
- Keep methods short and focused; refactor if a method exceeds ~30 lines.
- Use dependency injection for external dependencies.
- Avoid static state unless necessary.