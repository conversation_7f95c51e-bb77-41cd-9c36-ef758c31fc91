﻿@page "/"
@using HarriTenantApp.Server.Services
@using HarriTenantApp.Server.Models
@inject IHarriTenantService TenantService

<PageTitle>HarriTenant Manager - Dashboard</PageTitle>

<div class="row mb-4">
    <div class="col-12">
        <div class="jumbotron bg-primary text-white p-5 rounded">
            <div class="container-fluid">
                <h1 class="display-4">
                    <i class="oi oi-people me-3"></i>
                    HarriTenant Manager
                </h1>
                <p class="lead">Manage your software tenant configurations with ease</p>
                <hr class="my-4 bg-white">
                <p class="mb-4">Centralized management for tenant authentication, URLs, and configuration settings.</p>
                <a class="btn btn-light btn-lg" href="/tenants" role="button">
                    <i class="oi oi-arrow-right me-2"></i>
                    Manage Tenants
                </a>
            </div>
        </div>
    </div>
</div>

@if (loading)
{
    <div class="text-center py-3">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading dashboard...</span>
        </div>
    </div>
}
else
{
    <div class="row">
        <!-- Statistics Cards -->
        <div class="col-md-3 mb-4">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <i class="oi oi-people display-4 text-primary mb-2"></i>
                    <h3 class="card-title text-primary">@totalTenants</h3>
                    <p class="card-text text-muted">Total Tenants</p>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card border-success">
                <div class="card-body text-center">
                    <i class="oi oi-circle-check display-4 text-success mb-2"></i>
                    <h3 class="card-title text-success">@activeTenants</h3>
                    <p class="card-text text-muted">Active Tenants</p>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card border-info">
                <div class="card-body text-center">
                    <i class="oi oi-briefcase display-4 text-info mb-2"></i>
                    <h3 class="card-title text-info">@corporateTenants</h3>
                    <p class="card-text text-muted">Corporate Tenants</p>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <i class="oi oi-warning display-4 text-warning mb-2"></i>
                    <h3 class="card-title text-warning">@inactiveTenants</h3>
                    <p class="card-text text-muted">Inactive Tenants</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="oi oi-dashboard me-2"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <a href="/tenants/create" class="btn btn-primary w-100 py-3">
                                <i class="oi oi-plus me-2"></i>
                                Create New Tenant
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="/tenants" class="btn btn-outline-primary w-100 py-3">
                                <i class="oi oi-list me-2"></i>
                                View All Tenants
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="oi oi-info me-2"></i>
                        System Information
                    </h6>
                </div>
                <div class="card-body">
                    <dl class="row small mb-0">
                        <dt class="col-sm-6">Database:</dt>
                        <dd class="col-sm-6">Connected</dd>

                        <dt class="col-sm-6">Last Sync:</dt>
                        <dd class="col-sm-6">@DateTime.Now.ToString("HH:mm")</dd>

                        <dt class="col-sm-6">Version:</dt>
                        <dd class="col-sm-6">1.0.0</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private bool loading = true;
    private int totalTenants = 0;
    private int activeTenants = 0;
    private int inactiveTenants = 0;
    private int corporateTenants = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        loading = true;
        try
        {
            var result = await TenantService.GetAllTenantsAsync();
            if (result.IsSuccess && result.Data != null)
            {
                var tenants = result.Data.ToList();
                totalTenants = tenants.Count;
                activeTenants = tenants.Count(t => t.IsActive == true);
                inactiveTenants = tenants.Count(t => t.IsActive != true);
                corporateTenants = tenants.Count(t => t.IsCorporateTenant);
            }
        }
        catch (Exception)
        {
            // Handle error silently for dashboard
            totalTenants = 0;
            activeTenants = 0;
            inactiveTenants = 0;
            corporateTenants = 0;
        }
        finally
        {
            loading = false;
        }
    }
}
