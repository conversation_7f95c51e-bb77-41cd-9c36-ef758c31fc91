using AutoFixture.Xunit2;
using FluentAssertions;
using HarriTenantApp.Server.Models;
using HarriTenantApp.Server.Repositories;
using HarriTenantApp.Server.Services;
using Moq;

namespace HarriTenantApp.Server.Tests.Services
{
    public class HarriTenantServiceTests : TestBase
    {
        [Theory]
        [AutoMoqData]
        public async Task ShouldCreateHarriTenant_WhenValidDataProvided(
            [Frozen] IHarriTenantRepository repository,
            HarriTenant tenant,
            HarriTenantService sut)
        {
            // Arrange
            Mock.Get(repository)
                .Setup(x => x.CreateAsync(It.IsAny<HarriTenant>()))
                .ReturnsAsync(tenant);

            // Act
            var result = await sut.CreateTenantAsync(tenant);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().Be(tenant);
            result.ValidationErrors.Should().BeEmpty();
            
            Mock.Get(repository).Verify(x => x.CreateAsync(tenant), Times.Once);
        }

        [Theory]
        [AutoMoqData]
        public async Task ShouldReturnValidationErrors_WhenCreatingInvalidHarriTenant(
            [Frozen] IHarriTenantRepository repository,
            HarriTenantService sut)
        {
            // Arrange
            var invalidTenant = new HarriTenant
            {
                TenantId = Guid.NewGuid(),
                Client = "", // Invalid - empty client
                Secret = "123", // Invalid - too short
                TokenUrl = "invalid-url", // Invalid - not a valid URL
                BaseUrl = "invalid-url", // Invalid - not a valid URL
                IsCorporateTenant = false,
                IsActive = true
            };

            // Act
            var result = await sut.CreateTenantAsync(invalidTenant);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.ValidationErrors.Should().NotBeEmpty();
            result.ValidationErrors.Should().Contain(error => error.Contains("Client"));
            result.ValidationErrors.Should().Contain(error => error.Contains("Secret"));
            result.ValidationErrors.Should().Contain(error => error.Contains("URL"));
            
            Mock.Get(repository).Verify(x => x.CreateAsync(It.IsAny<HarriTenant>()), Times.Never);
        }

        [Theory]
        [AutoMoqData]
        public async Task ShouldRetrieveAllHarriTenants_WhenCalled(
            [Frozen] IHarriTenantRepository repository,
            List<HarriTenant> tenants,
            HarriTenantService sut)
        {
            // Arrange
            Mock.Get(repository)
                .Setup(x => x.GetAllAsync())
                .ReturnsAsync(tenants);

            // Act
            var result = await sut.GetAllTenantsAsync();

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().BeEquivalentTo(tenants);
            
            Mock.Get(repository).Verify(x => x.GetAllAsync(), Times.Once);
        }

        [Theory]
        [AutoMoqData]
        public async Task ShouldRetrieveHarriTenantById_WhenRecordExists(
            [Frozen] IHarriTenantRepository repository,
            HarriTenant tenant,
            HarriTenantService sut)
        {
            // Arrange
            Mock.Get(repository)
                .Setup(x => x.GetByIdAsync(tenant.TenantId))
                .ReturnsAsync(tenant);

            // Act
            var result = await sut.GetTenantByIdAsync(tenant.TenantId);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().Be(tenant);
            
            Mock.Get(repository).Verify(x => x.GetByIdAsync(tenant.TenantId), Times.Once);
        }

        [Theory]
        [AutoMoqData]
        public async Task ShouldReturnNotFound_WhenHarriTenantDoesNotExist(
            [Frozen] IHarriTenantRepository repository,
            Guid nonExistentId,
            HarriTenantService sut)
        {
            // Arrange
            Mock.Get(repository)
                .Setup(x => x.GetByIdAsync(nonExistentId))
                .ReturnsAsync((HarriTenant?)null);

            // Act
            var result = await sut.GetTenantByIdAsync(nonExistentId);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.ErrorMessage.Should().Contain("not found");
            result.Data.Should().BeNull();
            
            Mock.Get(repository).Verify(x => x.GetByIdAsync(nonExistentId), Times.Once);
        }

        [Theory]
        [AutoMoqData]
        public async Task ShouldUpdateHarriTenant_WhenValidChangesProvided(
            [Frozen] IHarriTenantRepository repository,
            HarriTenant existingTenant,
            HarriTenant updatedTenant,
            HarriTenantService sut)
        {
            // Arrange
            updatedTenant.TenantId = existingTenant.TenantId; // Ensure same ID
            
            Mock.Get(repository)
                .Setup(x => x.GetByIdAsync(existingTenant.TenantId))
                .ReturnsAsync(existingTenant);
            
            Mock.Get(repository)
                .Setup(x => x.UpdateAsync(It.IsAny<HarriTenant>()))
                .ReturnsAsync(updatedTenant);

            // Act
            var result = await sut.UpdateTenantAsync(updatedTenant);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().Be(updatedTenant);
            
            Mock.Get(repository).Verify(x => x.GetByIdAsync(existingTenant.TenantId), Times.Once);
            Mock.Get(repository).Verify(x => x.UpdateAsync(updatedTenant), Times.Once);
        }

        [Theory]
        [AutoMoqData]
        public async Task ShouldReturnNotFound_WhenUpdatingNonExistentHarriTenant(
            [Frozen] IHarriTenantRepository repository,
            HarriTenant tenant,
            HarriTenantService sut)
        {
            // Arrange
            Mock.Get(repository)
                .Setup(x => x.GetByIdAsync(tenant.TenantId))
                .ReturnsAsync((HarriTenant?)null);

            // Act
            var result = await sut.UpdateTenantAsync(tenant);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.ErrorMessage.Should().Contain("not found");
            
            Mock.Get(repository).Verify(x => x.GetByIdAsync(tenant.TenantId), Times.Once);
            Mock.Get(repository).Verify(x => x.UpdateAsync(It.IsAny<HarriTenant>()), Times.Never);
        }

        [Theory]
        [AutoMoqData]
        public async Task ShouldDeleteHarriTenant_WhenRecordExists(
            [Frozen] IHarriTenantRepository repository,
            HarriTenant tenant,
            HarriTenantService sut)
        {
            // Arrange
            Mock.Get(repository)
                .Setup(x => x.GetByIdAsync(tenant.TenantId))
                .ReturnsAsync(tenant);
            
            Mock.Get(repository)
                .Setup(x => x.DeleteAsync(tenant.TenantId))
                .ReturnsAsync(true);

            // Act
            var result = await sut.DeleteTenantAsync(tenant.TenantId);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            
            Mock.Get(repository).Verify(x => x.GetByIdAsync(tenant.TenantId), Times.Once);
            Mock.Get(repository).Verify(x => x.DeleteAsync(tenant.TenantId), Times.Once);
        }

        [Theory]
        [AutoMoqData]
        public async Task ShouldReturnNotFound_WhenDeletingNonExistentHarriTenant(
            [Frozen] IHarriTenantRepository repository,
            Guid nonExistentId,
            HarriTenantService sut)
        {
            // Arrange
            Mock.Get(repository)
                .Setup(x => x.GetByIdAsync(nonExistentId))
                .ReturnsAsync((HarriTenant?)null);

            // Act
            var result = await sut.DeleteTenantAsync(nonExistentId);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.ErrorMessage.Should().Contain("not found");
            
            Mock.Get(repository).Verify(x => x.GetByIdAsync(nonExistentId), Times.Once);
            Mock.Get(repository).Verify(x => x.DeleteAsync(It.IsAny<Guid>()), Times.Never);
        }

        [Theory]
        [AutoMoqData]
        public async Task ShouldHandleRepositoryExceptions_WhenDatabaseErrorOccurs(
            [Frozen] IHarriTenantRepository repository,
            HarriTenant tenant,
            HarriTenantService sut)
        {
            // Arrange
            Mock.Get(repository)
                .Setup(x => x.CreateAsync(It.IsAny<HarriTenant>()))
                .ThrowsAsync(new InvalidOperationException("Database connection failed"));

            // Act
            var result = await sut.CreateTenantAsync(tenant);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.ErrorMessage.Should().Contain("Database connection failed");
        }
    }
}
